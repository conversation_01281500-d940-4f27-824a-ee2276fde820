server {
        server_name             chat-assist.wenarratebrands.com;
        listen                  80;
        client_max_body_size 50m;

        location /v1 {
                proxy_pass http://chat-assist-backend:10070;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /static {
                proxy_pass http://chat-assist-backend:10070;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
        }

        location ~ ^/chat$ {
                proxy_pass http://chat-assist-backend:37861;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
        }

        root /usr/share/nginx/html;
        location ~* ^.+\.(css|js|txt|xml|swf|wav)$ {
                access_log off;
                expires 24h;
	}

        location / {
                index index.html index.htm;
                try_files $uri $uri/ /index.html;
        }
}

#server {
#  server_name             chat-assist.wenarratebrands.com;
#  listen                  443 ssl;
#  ssl_certificate         /etc/ssl/cert.pem;
#  ssl_certificate_key     /etc/ssl/key.pem;
#  ssl_protocols           TLSv1 TLSv1.1 TLSv1.2 TLSv1.3;
#  ssl_ciphers             HIGH:!aNULL:!MD5;
#  client_max_body_size 50m;

#  location /chat {
#    proxy_pass http://chat-assist-backend:10070;
#    proxy_set_header Host $host;
#    proxy_set_header X-Real-IP $remote_addr;
#    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#    proxy_set_header X-Forwarded-Proto $scheme;
#  }

#  location /v1 {
#    proxy_pass http://chat-assist-backend:10070;

#    proxy_connect_timeout 60s;   # 连接超时时间
#    proxy_read_timeout 600s;     # 读取超时时间
#    proxy_send_timeout 600s;     # 发送超时时间

#    proxy_set_header Host $host;
#    proxy_set_header X-Real-IP $remote_addr;
#    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#    proxy_set_header X-Forwarded-Proto $scheme;
#  }

#  location /static {
#    proxy_pass http://chat-assist-backend:10070;
#    proxy_set_header Host $host;
#    proxy_set_header X-Real-IP $remote_addr;
#    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#    proxy_set_header X-Forwarded-Proto $scheme;
#  }

#  location / {
#    root /usr/share/nginx/html;
#    index index.html index.htm;
#    try_files $uri $uri/ /index.html;
#  }
#}
