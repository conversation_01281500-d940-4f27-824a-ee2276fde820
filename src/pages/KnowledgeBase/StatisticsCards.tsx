import { Card, CardContent } from "@/components/ui/card";
import { useTranslation } from "@/hooks/useTranslation";
import { FileTypeStatistics } from "@/services/api";
import {
  Database,
  MessageCircleQuestion,
  Globe,
  FileText,
} from "lucide-react";

interface StatisticsCardsProps {
  statistics: FileTypeStatistics[];
  totalItems: number;
}

export const StatisticsCards = ({ statistics, totalItems }: StatisticsCardsProps) => {
  const { t } = useTranslation();

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center">
            <div className="p-3 bg-blue-100 rounded-lg">
              <Database className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                {t("totalKnowledge")}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {totalItems}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center">
            <div className="p-3 bg-blue-100 rounded-lg">
              <MessageCircleQuestion className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                Total FAQs
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {statistics.filter((s) => s.type === "FAQ")[0]?.count || 0}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center">
            <div className="p-3 bg-green-100 rounded-lg">
              <Globe className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                {t("totalWebsites")}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {statistics.filter((s) => s.type === "Website")[0]?.count || 0}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center">
            <div className="p-3 bg-purple-100 rounded-lg">
              <FileText className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                {t("totalDocuments")}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {statistics.filter((s) => s.type === "Document")[0]?.count || 0}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
