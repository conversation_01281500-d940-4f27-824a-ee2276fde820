# Google Tag Manager Installation

Deploy and manage your chat assistant through Google Tag Manager for centralized control and advanced tracking.

## Overview

Benefits of GTM installation:
- **Centralized Management** - Control chat deployment from GTM dashboard
- **Advanced Tracking** - Integrate with Google Analytics and other tools
- **A/B Testing** - Easy testing of different chat configurations
- **Conditional Loading** - Load chat based on specific triggers
- **Team Collaboration** - Multiple team members can manage tags

---

## Prerequisites

- Google Tag Manager account and container
- GTM container installed on your website
- Your unique Project ID from the dashboard
- Basic understanding of GTM concepts (tags, triggers, variables)

---

## Basic Installation

### Step 1: Create Custom HTML Tag

1. **Access GTM Dashboard**
   - Log in to [tagmanager.google.com](https://tagmanager.google.com)
   - Select your container

2. **Create New Tag**
   - Click **Tags** → **New**
   - Name it "Chat Assistant"
   - Choose **Custom HTML** as tag type

3. **Add Chat Script**
```html
<script async
  src="https://your-chat-script-url"
  project-id="project-id">
</script>
```

### Step 2: Create Project ID Variable

1. **Create Variable**
   - Go to **Variables** → **New**
   - Name it "Project ID"
   - Choose **Constant** variable type
   - Value: `your-unique-project-id`

2. **Update Tag**
   - Replace `your-unique-project-id` with `{{Project ID}}`
   - This allows easy management of the Project ID

### Step 3: Set Up Trigger

1. **Create Trigger**
   - Go to **Triggers** → **New**
   - Name it "All Pages - Chat"
   - Choose **Page View** trigger type
   - Select **All Page Views**

2. **Attach to Tag**
   - Go back to your Chat Assistant tag
   - Set **Triggering** to "All Pages - Chat"

### Step 4: Test and Publish

1. **Preview Mode**
   - Click **Preview** to test
   - Visit your website to verify chat loads

2. **Publish**
   - Click **Submit** to publish changes
   - Add version name and description

---

## Advanced Configuration

### Conditional Loading

**Load chat only on specific pages:**

1. **Create Page Path Variable**
   - Variables → New → Built-In Variable
   - Enable "Page Path"

2. **Create Custom Trigger**
   - Triggers → New → Page View
   - Name: "Contact Page Only"
   - Trigger Type: Page View
   - This trigger fires on: Some Page Views
   - Condition: Page Path equals /contact

**Load chat for specific user segments:**

```html
<script>
// Check user conditions before loading chat
(function() {
    var shouldLoadChat = false;
    
    // Check if user is logged in
    if (document.cookie.indexOf('user_logged_in=true') !== -1) {
        shouldLoadChat = true;
    }
    
    // Check user location (if available)
    if ({{Country}} === 'United States') {
        shouldLoadChat = true;
    }
    
    // Check time of day
    var hour = new Date().getHours();
    if (hour >= 9 && hour <= 17) { // Business hours
        shouldLoadChat = true;
    }
    
    if (shouldLoadChat) {
        var script = document.createElement('script');
        script.async = true;
        script.src = 'https://your-chat-script-url';
        script.setAttribute('project-id', '{{Project ID}}');
        document.body.appendChild(script);
    }
})();
</script>
```

### A/B Testing Setup

**Create multiple chat variants:**

1. **Create Variables for Each Variant**
   - Project ID A: `project-variant-a`
   - Project ID B: `project-variant-b`

2. **Create Random Number Variable**
   - Variables → New → Custom JavaScript
   - Name: "Random Number"
   - Code: `function() { return Math.random(); }`

3. **Create A/B Test Tag**
```html
<script>
(function() {
    var randomNum = {{Random Number}};
    var projectId = randomNum < 0.5 ? '{{Project ID A}}' : '{{Project ID B}}';
    
    var script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-chat-script-url';
    script.setAttribute('project-id', chatId);
    
    // Track which variant was shown
    if (typeof gtag !== 'undefined') {
        gtag('event', 'chat_variant_shown', {
            'variant': randomNum < 0.5 ? 'A' : 'B'
        });
    }
    
    document.body.appendChild(script);
})();
</script>
```

---

## Analytics Integration

### Google Analytics 4 Integration

**Track chat interactions:**

1. **Create GA4 Event Tag**
   - Tags → New → Google Analytics: GA4 Event
   - Event Name: `chat_interaction`
   - Parameters:
     - `chat_action`: `{{Chat Action}}`
     - `chat_message`: `{{Chat Message}}`

2. **Create Custom Event Trigger**
   - Triggers → New → Custom Event
   - Event Name: `chat_event`

3. **Add Tracking to Chat**
```html
<script async
  src="https://your-chat-script-url"
  project-id="project-id">
</script>

<script>
// Listen for chat events
window.addEventListener('chat_event', function(e) {
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
        'event': 'chat_event',
        'chat_action': e.detail.action,
        'chat_message': e.detail.message,
        'chat_timestamp': new Date().toISOString()
    });
});
</script>
```

### Enhanced E-commerce Tracking

**For e-commerce sites:**

```html
<script>
// Enhanced chat with e-commerce context
(function() {
    var ecommerceData = {
        currency: '{{Currency}}',
        value: {{Cart Value}},
        items: {{Cart Items}}
    };
    
    var script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-chat-script-url';
    script.setAttribute('project-id', '{{Project ID}}');
        ecommerce: ecommerceData,
        analytics: {
            gtm: true,
            enhanced_ecommerce: true
        }
    }));
    
    document.body.appendChild(script);
})();
</script>
```

---

## Multi-Environment Setup

### Development/Staging/Production

**Create Environment Variable:**

1. **Variables → New → Lookup Table**
   - Name: "Project ID by Environment"
   - Input Variable: `{{Page Hostname}}`
   - Lookup Table:
     - `localhost` → `dev-project-id`
     - `staging.yoursite.com` → `staging-project-id`
     - `yoursite.com` → `production-project-id`
   - Default Value: `production-project-id`

2. **Update Tag to Use Environment Variable**
```html
<script async
  src="https://your-chat-script-url"
  project-id="project-id">
</script>
```

### Multi-Brand/Multi-Site

**For managing multiple brands:**

```html
<script>
(function() {
    var brandConfig = {
        'brand-a.com': {
            projectId: 'brand-a-project-id',
            theme: 'blue',
            language: 'en'
        },
        'brand-b.com': {
            projectId: 'brand-b-project-id',
            theme: 'red',
            language: 'es'
        }
    };
    
    var hostname = window.location.hostname;
    var config = brandConfig[hostname] || brandConfig['brand-a.com'];
    
    var script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-chat-script-url';
    script.setAttribute('project-id', config.chatId);
        theme: config.theme,
        language: config.language
    }));
    
    document.body.appendChild(script);
})();
</script>
```

---

## Performance Optimization

### Lazy Loading with GTM

**Load chat after user interaction:**

```html
<script>
(function() {
    var chatLoaded = false;
    
    function loadChat() {
        if (chatLoaded) return;
        chatLoaded = true;
        
        var script = document.createElement('script');
        script.async = true;
        script.src = 'https://your-chat-script-url';
        script.setAttribute('project-id', '{{Project ID}}');
        document.body.appendChild(script);
        
        // Track lazy load event
        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
            'event': 'chat_lazy_loaded',
            'chat_trigger': 'user_interaction'
        });
    }
    
    // Load on scroll
    var scrolled = false;
    window.addEventListener('scroll', function() {
        if (!scrolled && window.scrollY > 300) {
            scrolled = true;
            loadChat();
        }
    });
    
    // Load on any click
    document.addEventListener('click', loadChat, { once: true });
    
    // Load after 10 seconds as fallback
    setTimeout(loadChat, 10000);
})();
</script>
```

### Conditional Loading Based on Page Performance

```html
<script>
(function() {
    // Wait for page to be fully loaded
    window.addEventListener('load', function() {
        // Check page load performance
        var navigation = performance.getEntriesByType('navigation')[0];
        var loadTime = navigation.loadEventEnd - navigation.loadEventStart;
        
        // Only load chat if page loaded quickly (< 3 seconds)
        if (loadTime < 3000) {
            var script = document.createElement('script');
            script.async = true;
            script.src = 'https://your-chat-script-url';
            script.setAttribute('project-id', '{{Project ID}}');
            document.body.appendChild(script);
        }
    });
})();
</script>
```

---

## Debugging and Monitoring

### Debug Mode Setup

**Create Debug Variable:**

1. **Variables → New → Custom JavaScript**
   - Name: "Debug Mode"
   - Code: `function() { return window.location.search.indexOf('debug=true') !== -1; }`

2. **Add Debug Logging**
```html
<script>
(function() {
    var debugMode = {{Debug Mode}};
    
    function debugLog(message, data) {
        if (debugMode) {
            console.log('[Chat GTM Debug]', message, data);
        }
    }
    
    debugLog('Loading chat assistant', {
        projectId: '{{Project ID}}',
        page: window.location.href,
        timestamp: new Date().toISOString()
    });
    
    var script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-chat-script-url';
    script.setAttribute('project-id', '{{Project ID}}');
    
    script.onload = function() {
        debugLog('Chat script loaded successfully');
    };
    
    script.onerror = function() {
        debugLog('Chat script failed to load');
    };
    
    document.body.appendChild(script);
})();
</script>
```

### Error Tracking

```html
<script>
(function() {
    try {
        var script = document.createElement('script');
        script.async = true;
        script.src = 'https://your-chat-script-url';
        script.setAttribute('project-id', '{{Project ID}}');
        
        script.onerror = function() {
            // Track loading errors
            window.dataLayer = window.dataLayer || [];
            window.dataLayer.push({
                'event': 'chat_error',
                'error_type': 'script_load_failed',
                'project_id': '{{Project ID}}',
                'page_url': window.location.href
            });
        };
        
        document.body.appendChild(script);
    } catch (error) {
        // Track JavaScript errors
        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
            'event': 'chat_error',
            'error_type': 'javascript_error',
            'error_message': error.message,
            'page_url': window.location.href
        });
    }
})();
</script>
```

---

## Best Practices

### Tag Organization

1. **Naming Convention**
   - Use consistent naming: "Chat Assistant - [Purpose]"
   - Example: "Chat Assistant - Homepage", "Chat Assistant - Product Pages"

2. **Folder Structure**
   - Create folders for different chat configurations
   - Group related tags, triggers, and variables

3. **Documentation**
   - Add notes to tags explaining their purpose
   - Document any custom configurations

### Version Control

1. **Container Versions**
   - Always add meaningful version names
   - Include change descriptions
   - Test in preview before publishing

2. **Backup Strategy**
   - Export container configurations regularly
   - Keep backup of working configurations

### Team Management

1. **User Permissions**
   - Assign appropriate access levels
   - Use workspaces for team collaboration

2. **Change Management**
   - Establish approval process for changes
   - Test changes in preview mode first

---

## Troubleshooting

### Common GTM Issues

**Tag Not Firing**
- Check trigger configuration
- Verify trigger conditions are met
- Use GTM preview mode to debug

**Chat Not Loading**
- Check for JavaScript errors in console
- Verify Project ID variable is correct
- Test script URL accessibility

**Multiple Chat Instances**
- Ensure only one chat tag fires per page
- Check for conflicting triggers
- Use trigger exceptions if needed

**Performance Issues**
- Implement lazy loading
- Use conditional triggers
- Monitor page load impact

---
