import { useState, useEffect } from 'react';
import { getCurrentProduct, CurrentProductResponse } from '@/services/api';
import { useAuth } from '@/context/AuthContext';

interface UseCurrentProductReturn {
  currentProduct: CurrentProductResponse | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useCurrentProduct = (): UseCurrentProductReturn => {
  const [currentProduct, setCurrentProduct] = useState<CurrentProductResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, isLoading: authLoading } = useAuth();

  const fetchCurrentProduct = async () => {
    try {
      setLoading(true);
      setError(null);
      const product = await getCurrentProduct();
      setCurrentProduct(product);
    } catch (err) {
      console.error('Failed to fetch current product:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch current product');
      setCurrentProduct(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 只有在用户已认证且认证状态不在加载中时才获取产品信息
    if (isAuthenticated && !authLoading) {
      fetchCurrentProduct();
    } else if (!authLoading && !isAuthenticated) {
      // 如果用户未认证，清空产品信息
      setCurrentProduct(null);
      setError(null);
      setLoading(false);
    }
  }, [isAuthenticated, authLoading]);

  return {
    currentProduct,
    loading,
    error,
    refetch: fetchCurrentProduct,
  };
};
