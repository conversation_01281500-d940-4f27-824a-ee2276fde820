import store from "store2";

interface ProjectSettings {
  logo: string;
  name: string;
  color: string;
  welcomeMsg: string;
  suggestedEnable: boolean;
  suggestedQuestions: string[];
}

interface CreateProjectRequest {
  name: string;
  website: string;
  settings: ProjectSettings;
}

interface CreateProjectResponse {
  id: string;
  name: string;
  website: string;
  settings: ProjectSettings;
  createTime: string;
}

interface UpdateProjectRequest {
  id: string;
  name?: string;
  settings?: ProjectSettings;
}

interface Project {
  id: string;
  name: string;
  website: string;
  createTime: string;
  settings: ProjectSettings;
}

export const createProject = async (
  data: CreateProjectRequest
): Promise<CreateProjectResponse> => {
  console.log("token: ", store.get("token"));
  const response = await fetch("/v1/projects", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

export const updateProject = async (
  data: UpdateProjectRequest
): Promise<CreateProjectResponse> => {
  const response = await fetch(`/v1/projects/${data.id}`, {
    method: "PATCH",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

export const getProjects = async (): Promise<Project[]> => {
  const response = await fetch("/v1/projects", {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

export const getProject = async (id: string): Promise<Project> => {
  const response = await fetch(`/v1/projects/${id}`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

// 导出类型
export type {
  ProjectSettings,
  CreateProjectRequest,
  CreateProjectResponse,
  UpdateProjectRequest,
  Project,
};
