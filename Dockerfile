FROM node:18.19.0-alpine3.17 as build

RUN apk add --no-cache git

WORKDIR /app

# Install dependencies
COPY package.json package-lock.json ./


RUN npm ci --unsafe-perm=true


# Build
COPY .env .env
COPY tsconfig*.json ./
COPY *.config.* ./
COPY components.json ./
COPY index.html ./
COPY src/ src/
COPY public/ public/
COPY docs/ docs/

RUN npm run build:all

# Deploy
FROM nginx:stable-alpine

COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
