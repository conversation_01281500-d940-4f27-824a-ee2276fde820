import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useTranslation } from "@/hooks/useTranslation";
import {
  Type,
  AlignLeft,
  List,
  CheckSquare,
  Calendar,
  Hash,
  Mail,
  Phone,
  Heading1,
  FileText,
  Minus,
  User,
  MapPin,
  CreditCard,
  IdCard,
  Search
} from "lucide-react";
import { ComponentTemplate, ComponentCategory, FormComponent } from "../types/form-types";

interface ComponentLibraryProps {
  onAddComponent: (component: FormComponent) => void;
}

export const ComponentLibrary = ({ onAddComponent }: ComponentLibraryProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const { t } = useTranslation();

  // 基础组件
  const basicComponents: <PERSON>mpo<PERSON><PERSON><PERSON><PERSON>[] = [
    {
      type: 'text-input',
      name: t('textInput'),
      icon: Type,
      defaultProps: {
        label: t('textInput'),
        placeholder: t('enterText'),
        required: false,
      },
      description: t('singleLineTextInput')
    },
    {
      type: 'textarea',
      name: t('multilineText'),
      icon: AlignLeft,
      defaultProps: {
        label: t('multilineText'),
        placeholder: t('enterMultilineText'),
        required: false,
      },
      description: t('multilineTextInput')
    },
    {
      type: 'select',
      name: t('dropdown'),
      icon: List,
      defaultProps: {
        label: t('dropdown'),
        placeholder: t('pleaseSelect'),
        required: false,
        options: [
          { label: `${t('option')}1`, value: 'option1' },
          { label: `${t('option')}2`, value: 'option2' },
          { label: `${t('option')}3`, value: 'option3' }
        ]
      },
      description: t('dropdownSelect')
    },
    {
      type: 'radio',
      name: t('radioButton'),
      icon: CheckSquare,
      defaultProps: {
        label: t('radioButton'),
        required: false,
        options: [
          { label: `${t('option')}1`, value: 'option1' },
          { label: `${t('option')}2`, value: 'option2' }
        ]
      },
      description: t('radioButtonGroup')
    },
    {
      type: 'checkbox',
      name: t('checkbox'),
      icon: CheckSquare,
      defaultProps: {
        label: t('checkbox'),
        required: false,
        options: [
          { label: `${t('option')}1`, value: 'option1' },
          { label: `${t('option')}2`, value: 'option2' }
        ]
      },
      description: t('checkboxGroup')
    },
    {
      type: 'date-picker',
      name: t('datePicker'),
      icon: Calendar,
      defaultProps: {
        label: t('datePicker'),
        placeholder: t('selectDate'),
        required: false,
      },
      description: t('datePickerComponent')
    },
    {
      type: 'number-input',
      name: t('numberInput'),
      icon: Hash,
      defaultProps: {
        label: t('numberInput'),
        placeholder: t('enterNumber'),
        required: false,
      },
      description: t('numberInputBox')
    },
    {
      type: 'email-input',
      name: t('emailInput'),
      icon: Mail,
      defaultProps: {
        label: t('emailAddressLabel'),
        placeholder: t('enterEmail'),
        required: false,
      },
      description: t('emailInputBox')
    },
    {
      type: 'phone-input',
      name: t('phoneInput'),
      icon: Phone,
      defaultProps: {
        label: t('phoneNumber'),
        placeholder: t('enterPhone'),
        required: false,
      },
      description: t('phoneInputBox')
    },
    // {
    //   type: 'file-upload',
    //   name: '文件上传',
    //   icon: Upload,
    //   defaultProps: {
    //     label: '文件上传',
    //     placeholder: '点击或拖拽上传文件',
    //     required: false,
    //   },
    //   description: '文件上传组件'
    // }
  ];

  // 布局组件
  const layoutComponents: ComponentTemplate[] = [
    {
      type: 'heading',
      name: t('heading'),
      icon: Heading1,
      defaultProps: {
        label: t('titleText'),
        properties: { level: 1 }
      },
      description: t('headingText')
    },
    {
      type: 'paragraph',
      name: t('paragraph'),
      icon: FileText,
      defaultProps: {
        label: t('paragraphContent'),
      },
      description: t('paragraphText')
    },
    {
      type: 'divider',
      name: t('divider'),
      icon: Minus,
      defaultProps: {
        label: '',
      },
      description: t('dividerLine')
    },
    // {
    //   type: 'button',
    //   name: '按钮',
    //   icon: MousePointer,
    //   defaultProps: {
    //     label: '按钮',
    //     properties: { variant: 'primary' }
    //   },
    //   description: '按钮组件'
    // }
  ];

  // 复用组件
  const templateComponents: ComponentTemplate[] = [
    {
      type: 'full-name',
      name: t('fullName'),
      icon: User,
      defaultProps: {
        label: t('fullName'),
        required: true,
      },
      description: t('fullNameInput')
    },
    {
      type: 'contact-info',
      name: t('contactInfo'),
      icon: Phone,
      defaultProps: {
        label: t('contactInfo'),
        required: true,
      },
      description: t('phoneAndEmailInput')
    },
    {
      type: 'address',
      name: t('address'),
      icon: MapPin,
      defaultProps: {
        label: t('address'),
        required: false,
      },
      description: t('fullAddressInput')
    },
    {
      type: 'id-number',
      name: t('idNumber'),
      icon: IdCard,
      defaultProps: {
        label: t('idCardNumber'),
        placeholder: t('enterIdNumber'),
        required: false,
      },
      description: t('idNumberInput')
    },
    {
      type: 'bank-card',
      name: t('bankCard'),
      icon: CreditCard,
      defaultProps: {
        label: t('bankCardInfo'),
        required: false,
      },
      description: t('bankCardInput')
    }
  ];

  const categories: ComponentCategory[] = [
    {
      id: 'basic',
      name: t('basicComponents'),
      icon: Type,
      components: basicComponents
    },
    {
      id: 'layout',
      name: t('layoutComponents'),
      icon: FileText,
      components: layoutComponents
    },
    {
      id: 'template',
      name: t('templateComponents'),
      icon: User,
      components: templateComponents
    }
  ];

  const handleAddComponent = (template: ComponentTemplate) => {
    const component: FormComponent = {
      id: `${template.type}_${Date.now()}`,
      type: template.type,
      ...template.defaultProps,
    } as FormComponent;
    
    onAddComponent(component);
  };

  const filteredCategories = categories.map(category => ({
    ...category,
    components: category.components.filter(comp =>
      comp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      comp.description?.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.components.length > 0);

  return (
    <div className="h-full flex flex-col">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg">{t('componentLibrary')}</CardTitle>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder={t('searchComponents')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-y-auto">
        <Tabs defaultValue="basic" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">{t('basic')}</TabsTrigger>
            <TabsTrigger value="layout">{t('layout')}</TabsTrigger>
            <TabsTrigger value="template">{t('common')}</TabsTrigger>
          </TabsList>

          {categories.map(category => (
            <TabsContent key={category.id} value={category.id} className="space-y-2">
              {(searchQuery ? filteredCategories.find(c => c.id === category.id)?.components : category.components)?.map(component => (
                <Card 
                  key={component.type} 
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => handleAddComponent(component)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        {(() => {
                          const IconComponent = component.icon;
                          return <IconComponent className="w-4 h-4 text-blue-600" />;
                        })()}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{component.name}</h4>
                        <p className="text-xs text-gray-500 mt-1">{component.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </div>
  );
};
