// Documentation loader - reads markdown files at build time
import { Plugin } from 'vite';
import fs from 'fs';
import path from 'path';

export interface DocItem {
  id: string;
  title: string;
  content: string;
  filename: string;
}

// Document order and title mapping
const TITLE_MAP: Record<string, string> = {
  'installation': 'Installation Overview',
  'installation-website': 'Website Installation',
  'installation-framer': 'Framer Installation',
  'installation-wordpress': 'WordPress Installation',
  'installation-shopify': 'Shopify Installation',
  'installation-wix': 'Wix Installation',
  'installation-squarespace': 'Squarespace Installation',
  'installation-gtm': 'Google Tag Manager',
  'installation-webview': 'WebView (Mobile Apps)',
};

// Generate title from filename
function generateTitle(filename: string): string {
  const nameWithoutExt = filename.replace(/\.md$/, '');

  return TITLE_MAP[nameWithoutExt] || nameWithoutExt
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Extract title from markdown content
function extractTitleFromContent(content: string): string | null {
  const match = content.match(/^#\s+(.+)$/m);
  return match ? match[1].trim() : null;
}

// Load all documents
export function loadDocs(): DocItem[] {
  const docsDir = path.resolve(process.cwd(), 'docs');

  if (!fs.existsSync(docsDir)) {
    console.warn('docs directory does not exist');
    return [];
  }

  const files = fs.readdirSync(docsDir)
    .filter(file => file.endsWith('.md'));

  // Get the order from TITLE_MAP
  const orderedKeys = Object.keys(TITLE_MAP);

  // Sort files based on titleMap order
  const sortedFiles = files.sort((a, b) => {
    const idA = a.replace(/\.md$/, '');
    const idB = b.replace(/\.md$/, '');

    const indexA = orderedKeys.indexOf(idA);
    const indexB = orderedKeys.indexOf(idB);

    // If both files are in titleMap, sort by their order
    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }

    // If only one file is in titleMap, prioritize it
    if (indexA !== -1) return -1;
    if (indexB !== -1) return 1;

    // If neither file is in titleMap, sort alphabetically
    return a.localeCompare(b);
  });

  return sortedFiles.map(filename => {
    const filePath = path.join(docsDir, filename);
    const content = fs.readFileSync(filePath, 'utf-8');
    const id = filename.replace(/\.md$/, '');

    // Prefer title from content, otherwise generate from filename
    const titleFromContent = extractTitleFromContent(content);
    const title = titleFromContent || generateTitle(filename);

    return {
      id,
      title,
      content,
      filename
    };
  });
}

// Vite plugin: inject document data into virtual module
export function docsPlugin(): Plugin {
  const virtualModuleId = 'virtual:docs';
  const resolvedVirtualModuleId = '\0' + virtualModuleId;

  return {
    name: 'docs-loader',
    resolveId(id) {
      if (id === virtualModuleId) {
        return resolvedVirtualModuleId;
      }
    },
    load(id) {
      if (id === resolvedVirtualModuleId) {
        const docs = loadDocs();
        return `export default ${JSON.stringify(docs, null, 2)};`;
      }
    },
    // Watch docs directory for changes, hot reload
    configureServer(server) {
      const docsDir = path.resolve(process.cwd(), 'docs');
      if (fs.existsSync(docsDir)) {
        server.watcher.add(docsDir);
        server.watcher.on('change', (file) => {
          if (file.startsWith(docsDir) && file.endsWith('.md')) {
            const module = server.moduleGraph.getModuleById(resolvedVirtualModuleId);
            if (module) {
              server.reloadModule(module);
            }
          }
        });
      }
    }
  };
}
