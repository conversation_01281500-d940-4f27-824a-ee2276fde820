import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import {
  FileTypeStatistics,
  getKnowledges,
  getKnowledgesStatistics,
  KnowledgeResponse,
} from "@/services/api";
import { useEffect, useState } from "react";

export const useKnowledgeBase = (projectId: string | undefined) => {
  const { t } = useTranslation();
  const { toast } = useToast();

  // 通用状态
  const [activeTab, setActiveTab] = useState<"FAQ" | "Website" | "Document">("FAQ");

  // 统计信息
  const [statistics, setStatistics] = useState<FileTypeStatistics[]>([
    { type: "FAQ", count: 0 },
    { type: "Website", count: 0 },
    { type: "Document", count: 0 },
  ]);

  // 知识库数据
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeResponse[]>([]);

  // 获取统计信息
  const fetchStatistics = async () => {
    if (!projectId) return;

    try {
      const statisticsResponse = await getKnowledgesStatistics(projectId);
      setStatistics(statisticsResponse);
    } catch (error) {
      console.error("Failed to fetch statistics:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: "Failed to load statistics",
      });
    }
  };

  // 页面初始化时获取统计信息
  useEffect(() => {
    if (projectId) {
      fetchStatistics();
    }
  }, [projectId]);

  // 处理标签页切换
  const handleTabChange = (value: "FAQ" | "Website" | "Document") => {
    setActiveTab(value);
  };

  return {
    // 状态
    activeTab,
    statistics,

    // 方法
    handleTabChange,
    fetchStatistics,
  };
};
