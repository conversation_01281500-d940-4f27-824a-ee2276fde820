import { Toaster as Sonner } from "@/components/ui/sonner";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import {
  BrowserRouter,
  Route,
  BrowserRouter as Router,
  Routes,
} from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import { ProjectProvider } from "./context/ProjectContext";
import Account from "./pages/Account";
import BillingPlan from "./pages/BillingPlan";
import {
  default as Chatbot,
  default as ChatbotManagement,
} from "./pages/Channels/Chatbot";
import Project from "./pages/Channels/Chatbot/components/Project";
import Channels from "./pages/Channels/WhatsApp";
import ChatRecords from "./pages/ChatRecords";
import Dashboard from "./pages/Dashboard";
import Documentation from "./pages/Documentation";
import FormBuilderIndex, { FormPreview } from "./pages/FormBuilder/index";
import PublicForm from "./pages/FormBuilder/PublicForm";
import TestInstall from "./pages/Index";
import KnowledgeBase from "./pages/KnowledgeBase";
import Login from "./pages/Login";
import NotFound from "./pages/NotFound";
import OAuthCallback from "./pages/OAuthCallback";
import OnboardingStarter from "./pages/Onboarding/OnboardingStarter";
import OnboardingChatbot from "./pages/Onboarding/OnboardingChatbot";
import OnboardingFinal from "./pages/Onboarding/OnboardingFinal";
import OnboardingForm from "./pages/Onboarding/OnboardingForm";
import Register from "./pages/Register";
import Index from "./pages/Index";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <ProjectProvider>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route
                path="/auth/:provider/callback"
                element={<OAuthCallback />}
              />
              <Route path="/onboarding-starter" element={<OnboardingStarter />} />
              <Route path="/onboarding-form" element={<OnboardingForm />} />
              <Route path="/onboarding-chatbot" element={<OnboardingChatbot />} />
              <Route path="/onboarding-final" element={<OnboardingFinal />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/project" element={<Project />} />
              <Route path="/project/:id" element={<Project />} />
              <Route path="/knowledge" element={<KnowledgeBase />} />
              <Route path="/form-builder/*" element={<FormBuilderIndex />} />
              <Route path="/form/:formId" element={<PublicForm />} />
              <Route path="/form-preview/:formId" element={<FormPreview />} />
              <Route path="/channels/whatsapp" element={<Channels />} />
              <Route path="/channels/chatbot" element={<Chatbot />} />
              <Route path="/chats" element={<ChatbotManagement />} />
              <Route path="/chat-records" element={<ChatRecords />} />
              <Route path="/test-install" element={<TestInstall />} />
              <Route path="/documentation" element={<Documentation />} />
              <Route path="/documentation/:docId" element={<Documentation />} />
              <Route path="/account" element={<Account />} />
              <Route path="/billing" element={<BillingPlan />} />
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </ProjectProvider>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
