import { getWidgetConfig } from "@/config";

interface EmbedConfig {
  projectId?: string;
}

export const generateEmbedCode = (config: EmbedConfig): string => {
  const { projectId = config.projectId } = config;

  const c = getWidgetConfig();

  // 生成简化的嵌入代码，所有配置都在widget.js中处理
  return `<script async src="${c.api.baseUrl}/widget.js" project-id="${projectId}"></script>`;
};

export default generateEmbedCode;
