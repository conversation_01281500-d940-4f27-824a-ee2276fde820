// 通用的聊天组件样式系统
// 将 Tailwind CSS 类转换为内联样式，确保 React 组件和纯 JS Widget 样式完全一致

export interface ChatStyleConfig {
  color: string;
  compact?: boolean;
  className?: string;
}

// 基础样式工具函数
export const hexToRgba = (hex: string, alpha: number): string => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

// 注入 CSS 动画（只注入一次）
const injectAnimations = (() => {
  let injected = false;
  return () => {
    if (injected) return;
    injected = true;

    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }

      @keyframes pulse {
        0%, 80%, 100% {
          opacity: 0.3;
          transform: scale(0.8);
        }
        40% {
          opacity: 1;
          transform: scale(1);
        }
      }
    `;
    document.head.appendChild(style);
  };
})();

// 样式生成器
export const createChatStyles = (config: ChatStyleConfig) => {
  const { color, compact = false } = config;

  // 注入动画
  injectAnimations();

  return {
    // 主容器样式
    container: {
      backgroundColor: 'white',
      borderRadius: '8px',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      width: compact ? '320px' : '100%',
      display: 'flex',
      flexDirection: 'column' as const,
      overflow: 'hidden',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    },

    // 头部样式
    header: {
      padding: '12px',
      color: 'white',
      backgroundColor: color,
      borderTopLeftRadius: '8px',
      borderTopRightRadius: '8px',
    },

    headerContent: {
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
    },

    avatar: {
      width: '32px',
      height: '32px',
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderRadius: '50%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      overflow: 'hidden',
    },

    avatarImage: {
      width: '100%',
      height: '100%',
      objectFit: 'cover' as const,
      borderRadius: '50%',
    },

    avatarText: {
      fontSize: '12px',
      fontWeight: '500',
    },

    headerText: {
      flex: '1',
    },

    assistantName: {
      fontWeight: '500',
      fontSize: '14px',
      margin: '0',
    },

    statusText: {
      fontSize: '12px',
      opacity: '0.9',
      margin: '0',
    },

    // 消息区域样式
    messagesArea: {
      padding: '12px',
      gap: '12px',
      overflowY: 'auto' as const,
      flex: '1',
      minHeight: compact ? '256px' : '320px',
      backgroundColor: '#f9fafb',
    },

    messageContainer: {
      display: 'flex',
      marginBottom: '12px',
    },

    messageContainerUser: {
      justifyContent: 'flex-end',
    },

    messageContainerBot: {
      justifyContent: 'flex-start',
    },

    botAvatar: {
      width: '24px',
      height: '24px',
      backgroundColor: '#e5e7eb',
      borderRadius: '50%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      flexShrink: '0',
      marginRight: '8px',
    },

    botAvatarText: {
      fontSize: '12px',
    },

    messageBubble: {
      maxWidth: '288px', // max-w-xs equivalent
      padding: '8px',
      borderRadius: '8px',
    },

    messageBubbleUser: {
      color: 'white',
      backgroundColor: color,
    },

    messageBubbleBot: {
      backgroundColor: '#f3f4f6',
      color: '#374151',
    },

    messageText: {
      fontSize: '12px',
      lineHeight: '1.4',
      margin: '0',
      whiteSpace: 'pre-line' as const,
    },

    messageTime: {
      fontSize: '12px',
      opacity: '0.7',
      marginTop: '4px',
      margin: '0',
    },

    // 建议问题样式
    suggestedQuestionsContainer: {
      gap: '8px',
      marginTop: '8px',
    },

    suggestedQuestion: {
      display: 'block',
      width: '100%',
      textAlign: 'left' as const,
      padding: '8px',
      fontSize: '12px',
      border: `1px solid ${hexToRgba(color, 0.2)}`,
      borderRadius: '8px',
      backgroundColor: hexToRgba(color, 0.06),
      color: color,
      cursor: 'pointer',
      transition: 'all 0.2s',
      marginBottom: '8px',
    },

    suggestedQuestionHover: {
      backgroundColor: '#f9fafb',
    },

    suggestedQuestionDisabled: {
      opacity: '0.5',
      cursor: 'not-allowed',
    },

    // 输入区域样式
    inputArea: {
      marginTop: 'auto',
      padding: '12px',
      borderTop: '1px solid #e5e7eb',
      backgroundColor: 'white',
      borderBottomLeftRadius: '8px',
      borderBottomRightRadius: '8px',
    },

    inputContainer: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
    },

    input: {
      flex: '1',
      fontSize: '12px',
      padding: '8px',
      border: '1px solid #d1d5db',
      borderRadius: '8px',
      outline: 'none',
    },

    inputDisabled: {
      opacity: '0.5',
    },

    sendButton: {
      padding: '8px',
      borderRadius: '8px',
      color: 'white',
      backgroundColor: color,
      border: 'none',
      cursor: 'pointer',
      transition: 'opacity 0.2s',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },

    sendButtonHover: {
      opacity: '0.9',
    },

    sendButtonDisabled: {
      opacity: '0.5',
    },

    // 等待消息气泡样式
    loadingMessageContainer: {
      display: 'flex',
      alignItems: 'flex-start',
      gap: '8px',
      marginBottom: '16px',
    },

    loadingMessageBubble: {
      backgroundColor: '#f3f4f6',
      borderRadius: '12px',
      padding: '12px 16px',
      maxWidth: '70%',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
    },

    loadingSpinner: {
      animation: 'spin 1s linear infinite',
      transformOrigin: 'center',
      flexShrink: '0',
    },

    loadingText: {
      color: '#6b7280',
      fontSize: '12px',
    },

    loadingDots: {
      display: 'flex',
      gap: '2px',
      alignItems: 'center',
    },

    loadingDot: {
      width: '4px',
      height: '4px',
      borderRadius: '50%',
      backgroundColor: '#9ca3af',
      animation: 'pulse 1.5s ease-in-out infinite',
    },

    // 底部链接样式
    poweredBy: {
      fontSize: '12px',
      textAlign: 'center' as const,
      color: '#6b7280',
      marginTop: '8px',
    },

    poweredByLink: {
      color: '#4b5563',
      textDecoration: 'underline',
      cursor: 'pointer',
      transition: 'color 0.2s',
    },

    poweredByLinkHover: {
      color: '#1f2937',
    },

    // Widget 特有样式
    widgetToggleButton: {
      position: 'fixed' as const,
      bottom: '20px',
      right: '20px',
      width: '60px',
      height: '60px',
      borderRadius: '50%',
      backgroundColor: color,
      border: 'none',
      cursor: 'pointer',
      zIndex: '9999',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      boxShadow: '0 4px 16px rgba(0,0,0,0.15)',
      transition: 'transform 0.2s',
    },

    widgetToggleButtonHover: {
      transform: 'scale(1.05)',
    },

    widgetContainer: {
      position: 'fixed' as const,
      bottom: '20px',
      right: '20px',
      width: '350px',
      height: '500px',
      zIndex: '10000',
      display: 'none',
    },

    widgetContainerVisible: {
      display: 'flex',
    },

    widgetCloseButton: {
      background: 'none',
      border: 'none',
      color: 'white',
      fontSize: '20px',
      cursor: 'pointer',
      marginLeft: 'auto',
      padding: '0',
      width: '24px',
      height: '24px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },
  };
};

// SVG 图标
export const icons = {
  send: `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
  </svg>`,

  chat: `<svg width="24" height="24" fill="white" viewBox="0 0 24 24">
    <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
  </svg>`,

  loading: `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
    <path d="M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z" opacity=".25"/>
    <path d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"/>
  </svg>`,

  close: '×'
};
