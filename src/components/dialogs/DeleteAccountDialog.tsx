import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AlertTriangle, Trash2, Eye, EyeOff } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";

interface DeleteAccountDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (password: string) => Promise<void>;
  isLoading?: boolean;
}

export const DeleteAccountDialog: React.FC<DeleteAccountDialogProps> = ({
  open,
  onOpenChange,
  onConfirm,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const [passwordInput, setPasswordInput] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [passwordError, setPasswordError] = useState("");

  const handleConfirm = async () => {
    if (!passwordInput.trim()) {
      setPasswordError("Please enter password");
      return;
    }

    setIsDeleting(true);
    setPasswordError("");
    try {
      await onConfirm(passwordInput);
      onOpenChange(false);
    } catch (error) {
      // 如果删除失败，显示错误信息（可能是密码错误或其他原因）
      setPasswordError("Delete failed, please check if password is correct");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    setPasswordInput("");
    setPasswordError("");
    setShowPassword(false);
    onOpenChange(false);
  };



  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            {t("deleteAccount")}
          </DialogTitle>
          <DialogDescription className="text-left">
            {t("deleteAccountWarning")}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="password-confirm" className="text-sm font-medium">
              {"Confirm Password"}
            </Label>
            <div className="relative">
              <Input
                id="password-confirm"
                type={showPassword ? "text" : "password"}
                value={passwordInput}
                onChange={(e) => setPasswordInput(e.target.value)}
                placeholder={t("enterPassword")}
                className={
                  passwordError ? "border-red-300 focus:border-red-500" : ""
                }
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            </div>
            {passwordError && (
              <p className="text-sm text-red-600">{passwordError}</p>
            )}
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isDeleting || isLoading}
          >
            {t("cancel")}
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={!passwordInput.trim() || isDeleting || isLoading}
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            {isDeleting || isLoading
              ? t("deleting")
              : t("deleteAccount")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
