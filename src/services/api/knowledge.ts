import store from "store2";

interface UploadFileRequest {
  projectId: string;
  files: FileList;
}

interface UploadWebsiteRequest {
  projectId: string;
  websiteUrl: string;
}

interface GetKnowledgesRequest {
  projectId: string;
  type?: KnowledgeType;
  pageNumber?: number;
  pageSize?: number;
}

export type KnowledgeType = "FAQ" | "Website" | "Document";
export type FileParseStatus = "Parsing" | "Parsed" | "Failed";

export interface KnowledgeResponse {
  id: string;
  projectId: string;
  name: string;
  path: string;
  type: KnowledgeType;
  mimeType: string;
  size: string;
  status: FileParseStatus;
  createTime: string;
}

interface KnowledgeListResponse {
  items: KnowledgeResponse[];
  total: number;
}

export interface FileTypeStatistics {
  type: KnowledgeType;
  count: number;
}

export const uploadFile = async (data: UploadFileRequest): Promise<void> => {
  const formData = new FormData();
  formData.append("projectId", data.projectId);

  // 添加多个文件
  for (let i = 0; i < data.files.length; i++) {
    formData.append("files", data.files[i]);
  }

  const response = await fetch("/v1/addFiles", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      // 不设置Content-Type，让浏览器自动设置multipart/form-data
    },
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }
};

export const uploadWebsite = async (
  data: UploadWebsiteRequest
): Promise<void> => {
  const response = await fetch("/v1/addWebsite", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }
};

export const getKnowledges = async (
  data: GetKnowledgesRequest
): Promise<KnowledgeListResponse> => {
  const params = new URLSearchParams({
    projectId: data.projectId,
    ...(data.type && { type: data.type }),
    ...(data.pageNumber && { pageNumber: data.pageNumber.toString() }),
    ...(data.pageSize && { pageSize: data.pageSize.toString() }),
  });

  const response = await fetch(`/v1/files?${params}`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

export const getKnowledgesStatistics = async (
  projectId: string
): Promise<FileTypeStatistics[]> => {
  const params = new URLSearchParams({ projectId });

  const response = await fetch(`/v1/files/statistics?${params}`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

// 导出类型
export type {
  UploadFileRequest,
  UploadWebsiteRequest,
  GetKnowledgesRequest,
  KnowledgeListResponse,
};
