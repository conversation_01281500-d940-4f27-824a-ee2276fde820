import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getProjects } from '@/services/api';
import { useAuth } from '@/context/AuthContext';

interface Project {
  id: string;
  name: string;
  website: string;
  createTime: string;
  settings: {
    logo: string;
    name: string;
    color: string;
    welcomeMsg: string;
    suggestedEnable: boolean;
    suggestedQuestions: string[];
  };
}

interface ProjectContextType {
  currentProject: Project | null;
  projects: Project[];
  setCurrentProject: (project: Project) => void;
  loading: boolean;
  refreshProjects: () => Promise<void>;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

interface ProjectProviderProps {
  children: ReactNode;
}

export const ProjectProvider: React.FC<ProjectProviderProps> = ({ children }) => {
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const { isAuthenticated, isLoading: authLoading } = useAuth();

  const refreshProjects = async () => {
    try {
      setLoading(true);
      const projectList = await getProjects();
      setProjects(projectList);
      // 如果没有当前项目或当前项目不在列表中，设置第一个项目为当前项目
      if (!currentProject || !projectList.find(p => p.id === currentProject.id)) {
        if (projectList.length > 0) {
          setCurrentProject(projectList[0]);
        }
      }
    } catch (error) {
      console.error('ProjectContext: Failed to fetch projects:', error);
      setProjects([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 只有在用户已认证且认证状态不在加载中时才获取项目
    if (isAuthenticated && !authLoading) {
      refreshProjects();
    } else if (!authLoading && !isAuthenticated) {
      // 如果用户未认证，清空项目列表
      setProjects([]);
      setCurrentProject(null);
      setLoading(false);
    }
  }, [isAuthenticated, authLoading]);

  const value: ProjectContextType = {
    currentProject,
    projects,
    setCurrentProject,
    loading,
    refreshProjects,
  };

  return (
    <ProjectContext.Provider value={value}>
      {children}
    </ProjectContext.Provider>
  );
};

export const useProject = (): ProjectContextType => {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
};
