// 纯JavaScript版本的聊天组件，用于生成widget.js
import {
  ChatCore,
  ChatMessage,
  ChatConfig,
} from "../components/ChatRenderer/ChatCore";
import { getWidgetConfig } from "../config";
import { Chat<PERSON><PERSON><PERSON>, ChatRendererConfig } from "../components/ChatRenderer";

interface WidgetOptions {
  projectId?: string;
  assistantName?: string;
  welcomeMessage?: string;
  color?: string;
  logo?: string | null;
  suggestedQuestions?: string[];
}

interface WidgetConfig extends Required<WidgetOptions> {}

export class ChatWidget {
  private container: HTMLElement;
  private toggleButton: HTMLElement;
  private chatCore: ChatCore;
  private renderer: ChatRenderer;
  private config: WidgetConfig;
  private messages: ChatMessage[] = [];
  private inputValue = "";
  private isLoading = false;

  // 默认配置
  private static readonly DEFAULT_CONFIG: Required<WidgetOptions> = {
    projectId: "",
    assistantName: getWidgetConfig().widget.defaultAssistantName,
    welcomeMessage: getWidgetConfig().widget.defaultWelcomeMessage,
    color: getWidgetConfig().widget.defaultColor,
    logo: null,
    suggestedQuestions: getWidgetConfig().widget.defaultSuggestedQuestions,
  };

  constructor(options: WidgetOptions = {}) {
    this.config = { ...ChatWidget.DEFAULT_CONFIG, ...options };
    this.initialize();
  }

  private initialize(): void {
    // 先创建渲染器（不依赖 ChatCore）
    this.initializeRenderer();
    // 创建 DOM 元素
    this.createWidgetElements();
    // 初始化 ChatCore（会触发回调，需要 container 已存在）
    this.initializeChatCore();
    // 附加到 DOM
    this.attachToDOM();
  }

  private initializeChatCore(): void {
    const widgetConfig = getWidgetConfig();
    const chatConfig: ChatConfig = {
      projectId: this.config.projectId || undefined,
      assistantName: this.config.assistantName,
      welcomeMessage: this.config.welcomeMessage,
      color: this.config.color,
      logo: this.config.logo,
      suggestedQuestions: this.config.suggestedQuestions,
      chatApiUrl: widgetConfig.api.chatEndpoint,
      sessionApiUrl: widgetConfig.api.sessionEndpoint,
    };

    const callbacks = {
      onMessageUpdate: this.handleMessageUpdate.bind(this),
      onLoadingChange: this.handleLoadingChange.bind(this),
    };

    this.chatCore = new ChatCore(chatConfig, callbacks);
  }

  private handleMessageUpdate(messages: ChatMessage[]): void {
    this.messages = messages;
    this.updateRenderer();
  }

  private handleLoadingChange(isLoading: boolean): void {
    this.isLoading = isLoading;
    this.updateRenderer();
  }

  private initializeRenderer(): void {
    const rendererConfig: ChatRendererConfig = {
      assistantName: this.config.assistantName,
      color: this.config.color,
      logo: this.config.logo,
      showInput: true,
      suggestedQuestions: this.config.suggestedQuestions,
      inputValue: this.inputValue,
      messages: this.messages,
      isLoading: this.isLoading,
      showCloseButton: true,
      onSendMessage: this.handleSendMessage.bind(this),
      onSuggestedQuestion: this.handleSendMessage.bind(this),
      onInputChange: this.handleInputChange.bind(this),
      onKeyDown: this.handleKeyDown.bind(this),
      onClose: this.hide.bind(this),
    };

    this.renderer = new ChatRenderer(rendererConfig);
  }

  private handleSendMessage(message: string): void {
    this.sendMessage(message);
  }

  private handleInputChange(value: string): void {
    this.inputValue = value;
    // 不需要立即重新渲染，避免输入框失去焦点
  }

  private handleKeyDown(event: KeyboardEvent): void {
    if (event.key === "Enter" && !this.isLoading && this.inputValue.trim()) {
      this.sendMessage(this.inputValue);
    }
  }

  private async sendMessage(message: string): Promise<void> {
    if (!message.trim() || this.isLoading) return;

    // 清空输入框
    this.inputValue = "";
    this.updateInputValue("");

    try {
      await this.chatCore.sendMessage(message);
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  }

  private updateInputValue(value: string): void {
    if (!this.container) {
      return;
    }

    const currentInput = this.container.querySelector(
      "input"
    ) as HTMLInputElement;
    if (currentInput) {
      currentInput.value = value;
    }
  }

  private updateRenderer(): void {
    // 如果容器还没创建，跳过渲染
    if (!this.container) {
      return;
    }

    // 保存当前输入框的焦点状态
    const inputState = this.saveInputState();

    // 更新渲染器配置
    this.renderer.updateConfig({
      isLoading: this.isLoading,
      inputValue: this.inputValue,
      messages: this.messages,
    });

    // 重新渲染容器内容
    this.renderContainer();

    // 恢复输入框的焦点状态
    this.restoreInputState(inputState);
  }

  private saveInputState(): {
    hadFocus: boolean;
    cursorPosition: number | null;
  } {
    if (!this.container) {
      return { hadFocus: false, cursorPosition: null };
    }

    const currentInput = this.container.querySelector(
      "input"
    ) as HTMLInputElement;
    return {
      hadFocus: currentInput && document.activeElement === currentInput,
      cursorPosition: currentInput ? currentInput.selectionStart : null,
    };
  }

  private renderContainer(): void {
    const newContent = this.renderer.render();
    this.container.innerHTML = "";
    this.container.appendChild(newContent);
  }

  private restoreInputState(inputState: {
    hadFocus: boolean;
    cursorPosition: number | null;
  }): void {
    if (inputState.hadFocus) {
      const newInput = this.container.querySelector(
        "input"
      ) as HTMLInputElement;
      if (newInput) {
        newInput.focus();
        if (inputState.cursorPosition !== null) {
          newInput.setSelectionRange(
            inputState.cursorPosition,
            inputState.cursorPosition
          );
        }
      }
    }
  }

  private createWidgetElements(): void {
    this.createContainer();
    this.createToggleButton();
    this.updateRenderer();
  }

  private createContainer(): void {
    this.container = document.createElement("div");
    this.container.id = "ai-chat-widget";
    this.container.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 350px;
      height: 500px;
      z-index: 10000;
      display: none;
      flex-direction: column;
    `;
  }

  private createToggleButton(): void {
    this.toggleButton = this.renderer.createToggleButton(this.show.bind(this));
    this.toggleButton.id = "ai-chat-toggle";
  }

  private attachToDOM(): void {
    document.body.appendChild(this.container);
    document.body.appendChild(this.toggleButton);
  }

  public show(): void {
    this.container.style.display = "flex";
    this.toggleButton.style.display = "none";
  }

  public hide(): void {
    this.container.style.display = "none";
    this.toggleButton.style.display = "flex";
  }

  public destroy(): void {
    this.container.remove();
    this.toggleButton.remove();
  }

  public updateConfig(newConfig: Partial<WidgetOptions>): void {
    // 合并新配置
    this.config = { ...this.config, ...newConfig };

    // 更新渲染器配置
    this.updateRendererConfig();

    // 更新聊天核心配置
    this.updateChatCoreConfig();

    // 重新渲染
    this.updateRenderer();
  }

  private updateRendererConfig(): void {
    this.renderer.updateConfig({
      assistantName: this.config.assistantName,
      color: this.config.color,
      logo: this.config.logo,
      suggestedQuestions: this.config.suggestedQuestions,
    });
  }

  private updateChatCoreConfig(): void {
    this.chatCore.updateConfig({
      assistantName: this.config.assistantName,
      welcomeMessage: this.config.welcomeMessage,
      color: this.config.color,
      logo: this.config.logo,
      suggestedQuestions: this.config.suggestedQuestions,
      projectId: this.config.projectId || undefined,
      chatApiUrl: getWidgetConfig().api.chatEndpoint,
    });
  }
}
