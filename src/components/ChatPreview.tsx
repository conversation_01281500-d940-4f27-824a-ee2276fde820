import { useState, useEffect, useRef } from 'react';
import { Send } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { ChatCore, ChatMessage, ChatConfig } from './ChatRenderer/ChatCore';
import { getChatApiUrl, getSessionApiUrl } from '@/config';
import { createChatStyles } from '@/components/ChatRenderer/ChatStyles';

interface ChatPreviewProps {
  assistantName?: string;
  welcomeMessage?: string;
  color?: string;
  suggestedQuestions?: string[];
  showInput?: boolean;
  compact?: boolean;
  className?: string;
  logo?: string | null;
  projectId?: string;
  initialSessionId?: string;
  notCreateSession?: boolean;
}

export const ChatPreview = ({
                              assistantName = 'Hali',
                              welcomeMessage = "Hi! I'm here to help! What would you like to know?",
                              color = '#FF7A51',
                              suggestedQuestions = ['I have another question'],
                              showInput = true,
                              compact = false,
                              className = '',
                              logo = null,
                              projectId = '',
                              initialSessionId = '',
                              notCreateSession = false
                            }: ChatPreviewProps) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [_sessionId, setSessionId] = useState(initialSessionId);
  const chatCoreRef = useRef<ChatCore | null>(null);

  // 初始化ChatCore
  useEffect(() => {
    const config: ChatConfig = {
      projectId,
      assistantName,
      welcomeMessage,
      color,
      logo,
      suggestedQuestions,
      chatApiUrl: getChatApiUrl(),
      sessionApiUrl: getSessionApiUrl(),
      sessionId: initialSessionId,
      notCreateSession,
    };

    const callbacks = {
      onMessageUpdate: setMessages,
      onLoadingChange: setIsLoading,
      onSessionIdChange: setSessionId,
    };

    chatCoreRef.current = new ChatCore(config, callbacks);
  }, []);

  // 更新配置当props改变时
  useEffect(() => {
    if (chatCoreRef.current) {
      chatCoreRef.current.updateConfig({
        assistantName,
        welcomeMessage,
        color,
        logo,
        suggestedQuestions,
        projectId,
        notCreateSession,
      });
    }
  }, [assistantName, welcomeMessage, color, logo, suggestedQuestions, projectId, notCreateSession]);

  const sendMessage = async () => {
    if (!chatCoreRef.current || !inputValue.trim()) return;

    const question = inputValue;
    setInputValue('');

    await chatCoreRef.current.sendMessage(question);
  };

  const handleSuggestedQuestion = async (question: string) => {
    if (!chatCoreRef.current) return;
    await chatCoreRef.current.sendMessage(question);
  };

  // 创建样式
  const styles = createChatStyles({ color, compact });

  return (
      <div
        className={className}
        style={{
          ...styles.container,
          height: '100%'
        }}
      >
        {/* Chat Header */}
        <div style={styles.header}>
          <div style={styles.headerContent}>
            <div style={styles.avatar}>
              {logo ? (
                  <img
                    src={logo}
                    alt="Assistant"
                    style={styles.avatarImage}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        parent.textContent = assistantName.charAt(0);
                        Object.assign(parent.style, styles.avatarText);
                      }
                    }}
                  />
              ) : (
                  <span style={styles.avatarText}>{assistantName.charAt(0)}</span>
              )}
            </div>
            <div style={styles.headerText}>
              <h4 style={styles.assistantName}>{assistantName}</h4>
              <p style={styles.statusText}>Active now</p>
            </div>
          </div>
        </div>

        {/* Messages */}
        <div style={styles.messagesArea}>
          {messages.map((msg, index) => {
            // 如果是最后一条消息，且是空的bot消息，且正在加载，则跳过渲染
            const isLastMessage = index === messages.length - 1;
            const isEmptyBotMessage = msg.type === 'bot' && !msg.message.trim();

            if (isLoading && isLastMessage && isEmptyBotMessage) {
              // 跳过空的bot消息，稍后用等待动画替换
              return null;
            }

            return (
              <div
                key={msg.id}
                style={{
                  ...styles.messageContainer,
                  ...(msg.type === 'user' ? styles.messageContainerUser : styles.messageContainerBot)
                }}
              >
                {msg.type === 'bot' && (
                    <div style={styles.botAvatar}>
                      <span style={styles.botAvatarText}>{assistantName.charAt(0)}</span>
                    </div>
                )}
                <div style={{
                  ...styles.messageBubble,
                  ...(msg.type === 'user' ? styles.messageBubbleUser : styles.messageBubbleBot)
                }}>
                  {msg.type === 'bot' ? (
                      <div style={styles.messageText}>
                        <ReactMarkdown>{String(msg.message || '')}</ReactMarkdown>
                      </div>
                  ) : (
                      <p style={styles.messageText}>{msg.message}</p>
                  )}
                  <p style={styles.messageTime}>{msg.time}</p>
                </div>
              </div>
            );
          })}

          {/* Loading Message */}
          {isLoading && (
              <div style={styles.loadingMessageContainer}>
                <div style={styles.botAvatar}>
                  <span style={styles.botAvatarText}>{assistantName.charAt(0)}</span>
                </div>
                <div style={styles.loadingMessageBubble}>
                  <span style={styles.loadingText}>Thinking</span>
                  <div style={styles.loadingDots}>
                    <div style={{...styles.loadingDot, animationDelay: '0s'}}></div>
                    <div style={{...styles.loadingDot, animationDelay: '0.2s'}}></div>
                    <div style={{...styles.loadingDot, animationDelay: '0.4s'}}></div>
                  </div>
                </div>
              </div>
          )}

          {/* Suggested Questions */}
          {!isLoading && suggestedQuestions.length > 0 && messages.length <= 2 && (
              <div style={styles.suggestedQuestionsContainer}>
                {suggestedQuestions.map((question, index) => (
                    <button
                        key={index}
                        onClick={() => handleSuggestedQuestion(question)}
                        style={styles.suggestedQuestion}
                        onMouseOver={(e) => {
                          Object.assign((e.target as HTMLElement).style, styles.suggestedQuestionHover);
                        }}
                        onMouseOut={(e) => {
                          Object.assign((e.target as HTMLElement).style, styles.suggestedQuestion);
                        }}
                    >
                      {question}
                    </button>
                ))}
              </div>
          )}
        </div>

        {/* Input Area */}
        {showInput && (
            <div style={styles.inputArea}>
              <div style={styles.inputContainer}>
                <input
                    type="text"
                    placeholder="Type your message..."
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && sendMessage()}
                    style={{
                      ...styles.input,
                      ...(isLoading ? styles.inputDisabled : {})
                    }}
                    disabled={isLoading}
                />
                <button
                    onClick={sendMessage}
                    style={{
                      ...styles.sendButton,
                      ...(isLoading ? styles.sendButtonDisabled : {})
                    }}
                    disabled={isLoading}
                    onMouseOver={(e) => {
                      if (!isLoading) {
                        Object.assign((e.target as HTMLElement).style, styles.sendButtonHover);
                      }
                    }}
                    onMouseOut={(e) => {
                      Object.assign((e.target as HTMLElement).style, styles.sendButton);
                    }}
                >
                  <Send style={{ width: '12px', height: '12px' }} />
                </button>
              </div>
              <div style={styles.poweredBy}>
                Powered by{' '}
                <a
                    href="/"
                    style={styles.poweredByLink}
                    onMouseOver={(e) => {
                      Object.assign((e.target as HTMLElement).style, styles.poweredByLinkHover);
                    }}
                    onMouseOut={(e) => {
                      Object.assign((e.target as HTMLElement).style, styles.poweredByLink);
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      window.location.href = '/';
                    }}
                >
                  🤖 AI Assistant
                </a>
              </div>
            </div>
        )}
      </div>
  );
};