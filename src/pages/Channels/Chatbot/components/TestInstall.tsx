import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardH<PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { TestTube, Copy, Check } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";
import { ChatPreview } from "@/components/ChatPreview";
import { useOnboardingData } from "@/hooks/useOnboardingData";
import { useAuth } from "@/context/AuthContext.tsx";
import { generateEmbedCode } from "@/utils/embedCode";
import { getProjects } from "@/services/api";
import { useNavigate } from "react-router-dom";
import { useProject } from "@/context/ProjectContext";

const TestInstall = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const { currentProject } = useProject();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { data } = useOnboardingData();
  const [projectData, setProjectData] = useState(null);
  const [isCopied, setIsCopied] = useState(false);

  // 获取项目数据
  useEffect(() => {
    const fetchProjectData = async () => {
      try {
        const projects = await getProjects();
        if (projects.length > 0) {
          setProjectData(projects[0]); // 使用第一个项目
        }
      } catch (error) {
        console.error("Failed to fetch projects:", error);
      }
    };

    if (isAuthenticated) {
      fetchProjectData();
    }
  }, [isAuthenticated]);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading, navigate]);

  // 使用当前项目数据或onboarding数据作为后备
  const currentData = currentProject || projectData || data;

  const embedCode = generateEmbedCode({
    projectId: currentProject?.id || projectData?.id,
  });

  const platforms = [
    {
      name: "Website Bubble",
      icon: "🌐",
      color: "bg-blue-100",
      url: "/documentation/installation-website",
    },
    {
      name: "Website Embed",
      icon: "🔗",
      color: "bg-green-100",
      url: "/documentation/installation-website",
    },
    {
      name: "Webflow",
      icon: "🌊",
      color: "bg-purple-100",
      url: "/documentation/installation-webflow",
    },
    {
      name: "Framer",
      icon: "⚡",
      color: "bg-black text-white",
      url: "/documentation/installation-framer",
    },
    {
      name: "Wix",
      icon: "W",
      color: "bg-yellow-100",
      url: "/documentation/installation-wix",
    },
    {
      name: "WordPress",
      icon: "W",
      color: "bg-blue-100",
      url: "/documentation/installation-wordpress",
    },
    {
      name: "Shopify",
      icon: "S",
      color: "bg-green-100",
      url: "/documentation/installation-shopify",
    },
    {
      name: "GTM",
      icon: "G",
      color: "bg-orange-100",
      url: "/documentation/installation-gtm",
    },
    {
      name: "Squarespace",
      icon: "S",
      color: "bg-gray-100",
      url: "/documentation/installation-squarespace",
    },
    {
      name: "WebView",
      icon: "📱",
      color: "bg-indigo-100",
      url: "/documentation/installation-webview",
    },
  ];

  const copyCode = async () => {
    try {
      await navigator.clipboard.writeText(embedCode);
      setIsCopied(true);

      // 2秒后重置状态
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);
    } catch (error) {
      console.error("复制失败:", error);
      // 降级方案：使用 document.execCommand
      try {
        const textArea = document.createElement("textarea");
        textArea.value = embedCode;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);

        setIsCopied(true);
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      } catch (fallbackError) {
        console.error("降级复制方案也失败:", fallbackError);
      }
    }
  };

  return (
    <>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <TestTube className="mr-2 h-6 w-6" />
              {t("testInstall")}
            </h1>
            <p className="text-gray-600 mt-1">{t("installChatbot")}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧：测试和代码 - 2/3宽度 */}
          <div className="lg:col-span-3 space-y-6">
            {/* 测试聊天机器人 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t("testChatbot")} - {t("testChatbotDescription")}
                </CardTitle>
                <p className="text-sm text-gray-600">
                  {t("embedCodeDescription")}
                </p>
              </CardHeader>
              <CardContent>
                <div className="relative bg-gray-50 border border-gray-200 rounded-lg overflow-hidden mb-4">
                  {/* 代码头部 */}
                  <div className="flex items-center justify-between px-4 py-2 bg-gray-100 border-b border-gray-200">
                    <span className="text-xs font-medium text-gray-600">
                      Embed Code
                    </span>
                    <button
                      onClick={copyCode}
                      className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded transition-all duration-200 ${
                        isCopied
                          ? "bg-green-100 text-green-800 border border-green-200"
                          : "bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
                      }`}
                      title={isCopied ? "Copied!" : "Copy"}
                    >
                      {isCopied ? (
                        <>
                          <Check className="h-3 w-3 mr-1" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="h-3 w-3 mr-1" />
                          Copy
                        </>
                      )}
                    </button>
                  </div>

                  {/* 代码内容 */}
                  <div className="p-4">
                    <pre className="text-sm font-mono text-gray-800 whitespace-pre-wrap break-all">
                      {embedCode}
                    </pre>
                  </div>
                </div>

                <p className="text-sm text-gray-600 mb-4">
                  See how to install the Web Chat widget with:
                </p>

                {/* 平台选择 */}
                <div className="grid grid-cols-2 gap-3">
                  {platforms.map((platform, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      className={`h-12 justify-start ${platform.color}`}
                      onClick={() => window.open(platform.url, '_blank')}
                    >
                      <span className="text-lg mr-2">{platform.icon}</span>
                      {platform.name}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：聊天预览 - 1/3宽度 */}
          <div className="space-y-6">
            {/* Web Chat 开关 */}
            {/* <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{t('webChatEnabled')}</span>
                  <Switch
                    checked={isWebChatEnabled}
                    onCheckedChange={setIsWebChatEnabled}
                  />
                </div>
              </CardContent>
            </Card> */}

            {/* 聊天窗口预览 */}
            <div style={{ width: "350px", height: "600px" }}>
              <ChatPreview
                assistantName={
                  currentData.settings?.name ||
                  currentData.name ||
                  currentData.brandName
                }
                welcomeMessage={
                  currentData.settings?.welcomeMsg || currentData.welcomeMessage
                }
                color={currentData.settings?.color || currentData.brandColor}
                suggestedQuestions={
                  currentData.settings?.suggestedEnable ??
                  currentData.suggestedQuestionsEnabled
                    ? currentData.settings?.suggestedQuestions ||
                      currentData.suggestedQuestions
                    : []
                }
                showInput={true}
                compact={false}
                className="h-full"
                logo={currentData.settings?.logo || currentData.logo}
                projectId={currentProject?.id || projectData?.id}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TestInstall;
