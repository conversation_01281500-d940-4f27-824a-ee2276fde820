import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { Calendar, ListOrdered, Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { formApi } from "../../../services/api/form";
import { FormConfig } from "../types/form-types";

interface FormSubmissionsModalProps {
  formId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const renderValue = (value: any, indent = 0): React.ReactNode => {
  if (typeof value === "object" && value !== null) {
    return (
      <div className="space-y-1 pl-4 border-l border-gray-200">
        {Object.entries(value).map(([subKey, subValue]) => (
          <div key={subKey} className="flex items-center text-sm">
            <span className="text-gray-600 min-w-[120px]">{subKey}:</span>
            <span className="text-gray-900 flex-1">
              {typeof subValue === "object"
                ? renderValue(subValue, indent + 1)
                : String(subValue)}
            </span>
          </div>
        ))}
      </div>
    );
  }
  return String(value);
};

export const FormSubmissionsModal = ({
  formId,
  open,
  onOpenChange,
}: FormSubmissionsModalProps) => {
  const { toast } = useToast();
  const { t } = useTranslation();

  const [formConfig, setFormConfig] = useState<FormConfig | null>(null);
  const [submissions, setSubmissions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const pageSize = 10;

  useEffect(() => {
    if (open && formId) {
      loadData();
    }
  }, [open, formId, currentPage]);

  const loadData = async () => {
    if (!formId) return;

    setLoading(true);
    setError(null);

    try {
      // 加载表单配置和提交数据
      const [config, submissionsData] = await Promise.all([
        formApi.loadForm(formId),
        formApi.getFormSubmissions(formId, currentPage, pageSize),
      ]);

      setFormConfig(config);
      setSubmissions(submissionsData.items || []);
      // 假设后端会在分页数据中返回总数
      // 如果后端没有返回总数，我们需要额外处理
      setTotalItems(submissionsData.total);
      setTotalPages(Math.ceil(submissionsData.total / pageSize));
    } catch (err) {
      setError(err instanceof Error ? err.message : t("loadSubmissionsFailed"));
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (!open) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {formConfig?.title ? formConfig.title : t("formSubmissions")}
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin" />
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-6">
            <div className="text-sm text-gray-500">
              {t("totalSubmissions")}: {totalItems}
            </div>

            {submissions.length === 0 ? (
              <Card>
                <CardContent className="py-12 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <ListOrdered className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {t("noSubmissionsYet")}
                  </h3>
                  <p className="text-gray-600">
                    {t("noSubmissionsDescription")}
                  </p>
                </CardContent>
              </Card>
            ) : (
              <>
                <div className="space-y-4">
                  {submissions.map((submission) => {
                    let data = {};
                    try {
                      data = JSON.parse(submission.content || "{}");
                    } catch (e) {
                      console.error("Error parsing submission content:", e);
                    }

                    return (
                      <Card key={submission.id}>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                          <span className="text-xs text-gray-500 flex items-center">
                            <Calendar className="w-3 h-3 mr-1" />
                            {new Date(submission.createTime).toLocaleString()}
                          </span>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            {Object.entries(data).map(([key, value]) => (
                              <div
                                key={key}
                                className="flex flex-col p-2 rounded-md hover:bg-gray-50"
                              >
                                <div className="flex items-center justify-between mb-1">
                                  <p className="text-sm font-medium text-gray-400">
                                    {key}
                                  </p>
                                </div>
                                <div className="text-sm text-gray-900">
                                  {renderValue(value)}
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
                
                {/* 分页控件 */}
                {totalPages > 1 && (
                  <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-4">
                    <div className="text-sm text-gray-600">
                      {t("showing")} {(currentPage - 1) * pageSize + 1} {t("to")}{" "}
                      {Math.min(currentPage * pageSize, totalItems)} {t("of")}{" "}
                      {totalItems} {t("items")}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Pagination className="mx-0">
                        <PaginationContent>
                          <PaginationItem>
                            <PaginationPrevious
                              onClick={() =>
                                currentPage > 1 && handlePageChange(currentPage - 1)
                              }
                              className={`${
                                currentPage <= 1
                                  ? "pointer-events-none opacity-50"
                                  : "cursor-pointer hover:bg-gray-100"
                              }`}
                            />
                          </PaginationItem>

                          {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                            let pageNumber: number;
                            if (totalPages <= 5) {
                              pageNumber = i + 1;
                            } else if (currentPage <= 3) {
                              pageNumber = i + 1;
                            } else if (currentPage >= totalPages - 2) {
                              pageNumber = totalPages - 4 + i;
                            } else {
                              pageNumber = currentPage - 2 + i;
                            }

                            return (
                              <PaginationItem key={pageNumber}>
                                <PaginationLink
                                  onClick={() => handlePageChange(pageNumber)}
                                  isActive={currentPage === pageNumber}
                                  className="cursor-pointer hover:bg-gray-100"
                                >
                                  {pageNumber}
                                </PaginationLink>
                              </PaginationItem>
                            );
                          })}

                          <PaginationItem>
                            <PaginationNext
                              onClick={() =>
                                currentPage < totalPages &&
                                handlePageChange(currentPage + 1)
                              }
                              className={`${
                                currentPage >= totalPages
                                  ? "pointer-events-none opacity-50"
                                  : "cursor-pointer hover:bg-gray-100"
                              }`}
                            />
                          </PaginationItem>
                        </PaginationContent>
                      </Pagination>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};