import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteConfirm } from "@/components/ui/delete-confirm";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import {
  getKnowledges,
  uploadWebsite,
  KnowledgeResponse,
} from "@/services/api";
import {
  ExternalLink,
  Filter,
  Globe,
  LinkIcon as Link,
  Loader2,
  Search,
  Trash2,
} from "lucide-react";
import { useEffect, useState } from "react";
import { KnowledgePagination } from "./KnowledgePagination";

interface WebsiteTabProps {
  projectId: string;
  onStatisticsUpdate: () => void;
}

export const WebsiteTab = ({
  projectId,
  onStatisticsUpdate,
}: WebsiteTabProps) => {
  const { t } = useTranslation();
  const { toast } = useToast();

  // Website相关状态
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [showWebsiteDialog, setShowWebsiteDialog] = useState(false);
  const [websiteUrl, setWebsiteUrl] = useState("");
  const [uploading, setUploading] = useState(false);

  // 分页和搜索状态
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const [totalItems, setTotalItems] = useState(0);

  // 获取知识库数据
  const fetchKnowledges = async (page: number = currentPage) => {
    if (!projectId) return;

    setLoading(true);
    try {
      const knowledgeResponse = await getKnowledges({
        projectId: projectId,
        type: "Website",
        pageNumber: page,
        pageSize: pageSize,
      });
      setKnowledgeItems(knowledgeResponse.items || []);
      setTotalItems(knowledgeResponse.total || 0);
    } catch (error) {
      console.error("Failed to fetch knowledges:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: t("failedToLoadKnowledge"),
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (projectId) {
      fetchKnowledges();
    }
  }, [projectId, currentPage, searchQuery]);

  // 处理搜索变化
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // 重置到第一页
  };

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 添加网站URL
  const handleAddWebsite = async () => {
    if (!websiteUrl.trim() || !projectId) return;

    setUploading(true);
    try {
      await uploadWebsite({
        projectId: projectId,
        websiteUrl: websiteUrl,
      });

      setWebsiteUrl("");
      setShowWebsiteDialog(false);

      toast({
        title: t("success"),
        description: t("addKnowledgeSuccess"),
      });

      // 重新获取知识库数据和统计信息
      fetchKnowledges(1);
      onStatisticsUpdate();
    } catch (error) {
      console.error("Failed to add website:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: t("addKnowledgeFailed"),
      });
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteItem = (id: string) => {
    setKnowledgeItems((prev) => prev.filter((item) => item.id !== id));
    // 重新获取数据和统计信息以更新总数
    fetchKnowledges(currentPage);
    onStatisticsUpdate();
  };

  // 过滤出网站类型的知识库项目
  const websiteItems = knowledgeItems.filter((item) => item.type === "Website");

  return (
    <div className="space-y-6">
      {/* Website操作按钮 */}
      <div className="flex gap-4">
        <Button
          className="flex items-center space-x-2"
          onClick={() => setShowWebsiteDialog(true)}
        >
          <Link className="w-4 h-4" />
          <span>{t("addWebsiteUrl")}</span>
        </Button>
      </div>

      {/* Website搜索 */}
      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder={t("search")}
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline" className="flex items-center space-x-2">
          <Filter className="w-4 h-4" />
          <span>{t("filter")}</span>
        </Button>
      </div>

      {/* Website表格 */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Loader2 className="w-8 h-8 text-gray-400 animate-spin mb-4" />
              <p className="text-gray-600">{t("loading")}</p>
            </div>
          ) : websiteItems.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Globe className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No websites found
              </h3>
              <p className="text-gray-600 mb-4">
                Start by adding your first website
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b bg-gray-50">
                  <tr>
                    <th className="text-left p-4 font-medium text-gray-600">
                      {t("title")}
                    </th>
                    <th className="text-left p-4 font-medium text-gray-600">
                      {t("status")}
                    </th>
                    <th className="text-left p-4 font-medium text-gray-600">
                      {t("lastTrained")}
                    </th>
                    <th className="text-left p-4 font-medium text-gray-600">
                      {t("actions")}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {websiteItems.map((item) => (
                    <tr key={item.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-gray-100 rounded">
                            <Globe className="w-4 h-4 text-gray-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">
                              {item.name}
                            </p>
                            <p className="text-sm text-gray-500">{item.path}</p>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge
                          variant="default"
                          className={
                            item.status === "Parsed"
                              ? "bg-green-100 text-green-800 border-green-200"
                              : item.status === "Parsing"
                              ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                              : "bg-red-100 text-red-800 border-red-200"
                          }
                        >
                          <span
                            className={`w-2 h-2 rounded-full mr-2 ${
                              item.status === "Parsed"
                                ? "bg-green-500"
                                : item.status === "Parsing"
                                ? "bg-yellow-500"
                                : "bg-red-500"
                            }`}
                          ></span>
                          {item.status}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <span className="text-sm text-gray-600">
                          {new Date(item.createTime).toLocaleString()}
                        </span>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center space-x-2">
                          <DeleteConfirm
                            title="Delete Website"
                            description="Are you sure you want to delete this website from the knowledge base? This action cannot be undone."
                            tooltipContent={t("deleteItem")}
                            onConfirm={() => handleDeleteItem(item.id)}
                          >
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </DeleteConfirm>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(item.path, "_blank")}
                              >
                                <ExternalLink className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{t("openLink")}</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 添加网站URL对话框 */}
      <Dialog open={showWebsiteDialog} onOpenChange={setShowWebsiteDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{t("addWebsite")}</DialogTitle>
            <DialogDescription>{t("addWebsiteDescription")}</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Input
                id="website-url"
                placeholder="https://example.com"
                value={websiteUrl}
                onChange={(e) => setWebsiteUrl(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleAddWebsite()}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowWebsiteDialog(false)}
            >
              {t("cancel")}
            </Button>
            <Button
              onClick={handleAddWebsite}
              disabled={!websiteUrl.trim() || uploading}
            >
              {uploading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {t("uploading")}
                </>
              ) : (
                t("addWebsite")
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 分页组件 */}
      <KnowledgePagination
        currentPage={currentPage}
        totalItems={totalItems}
        pageSize={pageSize}
        onPageChange={handlePageChange}
      />
    </div>
  );
};
