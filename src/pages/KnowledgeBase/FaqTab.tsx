import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteConfirm } from "@/components/ui/delete-confirm";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import {
  getFaqs,
  createFaq,
  updateFaq,
  deleteFaq,
  FaqResponse,
} from "@/services/api";
import {
  Loader2,
  Search,
  Trash2,
  MessageCircleQuestion,
  Plus,
  Edit,
} from "lucide-react";
import { useEffect, useState } from "react";
import { KnowledgePagination } from "./KnowledgePagination";

interface FaqTabProps {
  projectId: string;
  onStatisticsUpdate: () => void;
}

export const FaqTab = ({
  projectId,
  onStatisticsUpdate,
}: FaqTabProps) => {
  const { t } = useTranslation();
  const { toast } = useToast();

  // FAQ相关状态
  const [faqItems, setFaqItems] = useState<FaqResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [showFaqDialog, setShowFaqDialog] = useState(false);
  const [editingFaq, setEditingFaq] = useState<FaqResponse | null>(null);
  const [faqQuestion, setFaqQuestion] = useState("");
  const [faqAnswer, setFaqAnswer] = useState("");
  const [faqLoading, setFaqLoading] = useState(false);

  // 分页和搜索状态
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const [totalItems, setTotalItems] = useState(0);

  // 获取FAQ数据
  const fetchFaqs = async (page: number = currentPage) => {
    if (!projectId) return;

    setLoading(true);
    try {
      const response = await getFaqs({
        projectId: projectId,
        pageNumber: page.toString(),
        pageSize: pageSize.toString(),
        ...(searchQuery && { keyword: searchQuery }),
      });

      if (Array.isArray(response)) {
        setFaqItems(response);
        setTotalItems(response.length);
      } else {
        setFaqItems(response.items || []);
        setTotalItems(response.total || 0);
      }
    } catch (error) {
      console.error("Failed to fetch FAQs:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: "Failed to load FAQs",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (projectId) {
      fetchFaqs();
    }
  }, [projectId, currentPage, searchQuery]);

  // 处理搜索变化
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // 重置到第一页
  };

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // FAQ相关操作
  const handleAddFaq = () => {
    setEditingFaq(null);
    setFaqQuestion("");
    setFaqAnswer("");
    setShowFaqDialog(true);
  };

  const handleEditFaq = (faq: FaqResponse) => {
    setEditingFaq(faq);
    setFaqQuestion(faq.question);
    setFaqAnswer(faq.answer);
    setShowFaqDialog(true);
  };

  const handleSaveFaq = async () => {
    if (!projectId || !faqQuestion.trim() || !faqAnswer.trim()) return;

    setFaqLoading(true);
    try {
      if (editingFaq) {
        // 更新FAQ
        const updatedFaq = await updateFaq(editingFaq.id, {
          question: faqQuestion,
          answer: faqAnswer,
        });
        setFaqItems((prev) =>
          prev.map((item) => (item.id === editingFaq.id ? updatedFaq : item))
        );
        toast({
          title: t("success"),
          description: "FAQ updated successfully",
        });
      } else {
        // 创建新FAQ
        const newFaq = await createFaq({
          projectId: projectId,
          question: faqQuestion,
          answer: faqAnswer,
        });
        setFaqItems((prev) => [newFaq, ...prev]);
        toast({
          title: t("success"),
          description: "FAQ created successfully",
        });
      }

      setShowFaqDialog(false);
      setFaqQuestion("");
      setFaqAnswer("");
      setEditingFaq(null);
      onStatisticsUpdate();
    } catch (error) {
      console.error("Failed to save FAQ:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: "Failed to save FAQ",
      });
    } finally {
      setFaqLoading(false);
    }
  };

  const handleDeleteFaq = async (faqId: string) => {
    try {
      await deleteFaq(faqId);
      setFaqItems((prev) => prev.filter((item) => item.id !== faqId));
      toast({
        title: t("success"),
        description: "FAQ deleted successfully",
      });
      onStatisticsUpdate();
    } catch (error) {
      console.error("Failed to delete FAQ:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: "Failed to delete FAQ",
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* FAQ操作按钮 */}
      <div className="flex gap-4">
        <Button
          className="flex items-center space-x-2"
          onClick={handleAddFaq}
        >
          <Plus className="w-4 h-4" />
          <span>Add FAQ</span>
        </Button>
      </div>

      {/* FAQ搜索 */}
      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder={t("search")}
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* FAQ表格 */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Loader2 className="w-8 h-8 text-gray-400 animate-spin mb-4" />
              <p className="text-gray-600">{t("loading")}</p>
            </div>
          ) : faqItems.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <MessageCircleQuestion className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No FAQs found
              </h3>
              <p className="text-gray-600 mb-4">
                Start by adding your first FAQ
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b bg-gray-50">
                  <tr>
                    <th className="text-left p-4 font-medium text-gray-600 w-[30%]">
                      Question
                    </th>
                    <th className="text-left p-4 font-medium text-gray-600 w-[40%]">
                      Answer
                    </th>
                    <th className="text-left p-4 font-medium text-gray-600 w-[15%]">
                      Created
                    </th>
                    <th className="text-left p-4 font-medium text-gray-600 w-[15%]">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {faqItems.map((faq) => (
                    <tr
                      key={faq.id}
                      className="border-b hover:bg-gray-50"
                    >
                      <td className="p-4 w-[30%]">
                        <div className="text-sm text-gray-900">
                          {faq.question}
                        </div>
                      </td>
                      <td className="p-4 w-[40%]">
                        <div className="text-sm text-gray-600">
                          {faq.answer}
                        </div>
                      </td>
                      <td className="p-4 w-[15%]">
                        <span className="text-sm text-gray-600">
                          {new Date(faq.createTime).toLocaleString()}
                        </span>
                      </td>
                      <td className="p-4 w-[15%]">
                        <div className="flex items-center space-x-2">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditFaq(faq)}
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Edit FAQ</p>
                            </TooltipContent>
                          </Tooltip>
                          <DeleteConfirm
                            title="Delete FAQ"
                            description="Are you sure you want to delete this FAQ? This action cannot be undone."
                            tooltipContent="Delete FAQ"
                            onConfirm={() => handleDeleteFaq(faq.id)}
                          >
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </DeleteConfirm>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* FAQ对话框 */}
      <Dialog open={showFaqDialog} onOpenChange={setShowFaqDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{editingFaq ? "Edit FAQ" : "Add FAQ"}</DialogTitle>
            <DialogDescription>
              {editingFaq
                ? "Update the FAQ question and answer"
                : "Create a new FAQ with question and answer"}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="faq-question">Question</Label>
              <Input
                id="faq-question"
                placeholder="Enter the question..."
                value={faqQuestion}
                onChange={(e) => setFaqQuestion(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="faq-answer">Answer</Label>
              <textarea
                id="faq-answer"
                placeholder="Enter the answer..."
                value={faqAnswer}
                onChange={(e) => setFaqAnswer(e.target.value)}
                className="w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowFaqDialog(false);
                setFaqQuestion("");
                setFaqAnswer("");
                setEditingFaq(null);
              }}
            >
              {t("cancel")}
            </Button>
            <Button
              onClick={handleSaveFaq}
              disabled={
                !faqQuestion.trim() || !faqAnswer.trim() || faqLoading
              }
            >
              {faqLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {editingFaq ? "Updating..." : "Creating..."}
                </>
              ) : editingFaq ? (
                "Update FAQ"
              ) : (
                "Create FAQ"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 分页组件 */}
      <KnowledgePagination
        currentPage={currentPage}
        totalItems={totalItems}
        pageSize={pageSize}
        onPageChange={handlePageChange}
      />
    </div>
  );
};
