import { FormComponent, FormConfig, FormSettings } from "../types/form-types";

// 默认表单设置
export const defaultFormSettings: FormSettings = {
  submitText: "Submit",
  successMessage: "Form submitted successfully!",
  errorMessage: "Submitted failed, please try again.",
  allowMultipleSubmissions: true,
  requireLogin: false,
  collectEmail: false,
};

// 序列化表单配置为JSON
export const serializeForm = (
  formId: string,
  title: string,
  description: string,
  components: FormComponent[],
  settings: FormSettings = defaultFormSettings,
  logo?: string | null
): FormConfig => {
  const now = new Date().toISOString();

  return {
    formId,
    title,
    description,
    logo: logo || undefined,
    settings,
    components: components.map((component, index) => ({
      ...component,
      position: { x: 0, y: index }, // 确保位置信息正确
    })),
    url: "",
    active: false,
    createdAt: now,
    updatedAt: now,
  };
};

// 生成唯一的表单ID
export const generateFormId = (): string => {
  return `form_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
