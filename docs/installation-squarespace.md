# Squarespace Installation

Add the chat assistant to your Squarespace website using code injection.

## Overview

Squarespace installation methods:
- **Code Injection (Recommended)** - Site-wide installation
- **Code Block** - Page-specific installation
- **Developer Mode** - Advanced customization (Developer plan required)

---

## Method 1: Code Injection (Site-wide)

### Step 1: Access Code Injection Settings

1. **Open Squarespace Dashboard**
   - Log in to your Squarespace account
   - Go to your website dashboard

2. **Navigate to Code Injection**
   - Click **Settings** in the main menu
   - Select **Advanced** from the settings menu
   - Click **Code Injection**

### Step 2: Add Chat Script

1. **Add to Footer**
   - Scroll to the **Footer** section
   - Add your chat script:

```html
<!-- Chat Assistant -->
<script async
  src="https://your-chat-script-url"
  project-id="project-id">
</script>
```

2. **Save Changes**
   - Click **Save** at the top of the page
   - Changes will apply to all pages

### Step 3: Verify Installation

1. Visit your website
2. Check that the chat widget appears
3. Test the chat functionality
4. Verify it works on different pages

---

## Method 2: Code Block (Page-specific)

### Step 1: Edit Page

1. **Open Page Editor**
   - Go to **Pages** in your dashboard
   - Click **Edit** on the page you want to add chat to

2. **Add Code Block**
   - Click the **+** button to add content
   - Select **Code** from the content blocks

### Step 2: Configure Code Block

1. **Add Chat Script**
```html
<div id="squarespace-chat">
  <script async
    src="https://your-chat-script-url"
    project-id="project-id">
  </script>
</div>

<style>
#squarespace-chat {
  /* Hide the container since chat will be positioned absolutely */
  display: none;
}
</style>
```

2. **Save and Publish**
   - Click **Save** in the top left
   - Publish your changes

---

## Method 3: Developer Mode (Advanced)

### Prerequisites
- Squarespace Developer plan
- Git and Node.js installed
- Basic knowledge of Squarespace templates

### Step 1: Set Up Developer Environment

1. **Clone Template**
```bash
git clone https://github.com/squarespace/base-template.git
cd base-template
npm install
```

2. **Configure for Your Site**
```bash
squarespace setup
# Follow prompts to connect to your site
```

### Step 2: Add Chat to Template

1. **Edit Template Files**
   - Open `template.conf`
   - Add chat configuration

2. **Add to Footer Template**
   - Edit `blocks/footer.block`
   - Add chat script before closing tags:

```html
<!-- Chat Assistant -->
<script async
  src="https://your-chat-script-url"
  project-id="project-id">
</script>
```

3. **Create Chat ID Block**
   - Create `blocks/chat-id.block`
   - Add your chat ID:

```
your-unique-chat-id
```

### Step 3: Deploy Changes

```bash
squarespace deploy
```

---

## Squarespace-Specific Integration

### Commerce Integration

**For Squarespace Commerce sites:**

```html
<script>
// Add e-commerce context to chat
(function() {
    var ecommerceData = {};
    
    // Check if on product page
    if (document.body.classList.contains('ProductItem')) {
        var productTitle = document.querySelector('.ProductItem-details-title');
        if (productTitle) {
            ecommerceData.product = {
                name: productTitle.textContent.trim()
            };
        }
    }
    
    // Check if on cart page
    if (document.body.classList.contains('cart')) {
        var cartItems = document.querySelectorAll('.CartTable-row');
        ecommerceData.cart = {
            items: cartItems.length
        };
    }
    
    var script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-chat-script-url';
    script.setAttribute('project-id', 'your-unique-chat-id');
    
    if (Object.keys(ecommerceData).length > 0) {
            context: ecommerceData
        }));
    }
    
    document.body.appendChild(script);
})();
</script>
```

### Blog Integration

**Add context for blog posts:**

```html
<script>
(function() {
    var blogData = {};
    
    // Check if on blog post
    if (document.body.classList.contains('view-item')) {
        var postTitle = document.querySelector('.BlogItem-title');
        var postCategory = document.querySelector('.BlogItem-category');
        
        if (postTitle) {
            blogData.post = {
                title: postTitle.textContent.trim(),
                category: postCategory ? postCategory.textContent.trim() : 'General'
            };
        }
    }
    
    var script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-chat-script-url';
    script.setAttribute('project-id', 'your-unique-chat-id');
    
    if (Object.keys(blogData).length > 0) {
            context: blogData
        }));
    }
    
    document.body.appendChild(script);
})();
</script>
```

### Event Integration

**For event pages:**

```html
<script>
(function() {
    var eventData = {};
    
    // Check if on events page
    if (document.body.classList.contains('events')) {
        eventData.page = 'events';
        
        // Get upcoming events count
        var upcomingEvents = document.querySelectorAll('.eventlist-event--upcoming');
        if (upcomingEvents.length > 0) {
            eventData.upcomingEvents = upcomingEvents.length;
        }
    }
    
    var script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-chat-script-url';
    script.setAttribute('project-id', 'your-unique-chat-id');
    
    if (Object.keys(eventData).length > 0) {
            context: eventData
        }));
    }
    
    document.body.appendChild(script);
})();
</script>
```

---

## Styling for Squarespace

### Template-Specific Adjustments

```css
/* Add to Custom CSS in Design > Custom CSS */

/* General Squarespace adjustments */
#ai-chat-widget {
    z-index: 9999 !important;
}

/* Brine template family adjustments */
.tweak-site-ajax-loading-enable #ai-chat-widget {
    position: fixed !important;
}

/* Avenue template adjustments */
.Header--overlay #ai-chat-widget {
    z-index: 1000 !important;
}

/* Bedford template adjustments */
.collection-type-blog.view-list #ai-chat-widget {
    bottom: 30px !important;
}

/* Mobile adjustments for all templates */
@media screen and (max-width: 640px) {
    #ai-chat-widget {
        bottom: 20px !important;
        right: 20px !important;
        width: 60px !important;
        height: 60px !important;
    }
}

/* Hide chat on specific pages */
.collection-type-gallery #ai-chat-widget {
    display: none !important;
}

/* Adjust for mobile info bar */
.sqs-mobile-info-bar-enabled #ai-chat-widget {
    bottom: 70px !important;
}
```

### Custom Chat Styling

```css
/* Match Squarespace theme colors */
#ai-chat-widget-button {
    background: var(--accent-color) !important;
    border-radius: 50% !important;
}

.chat-window {
    font-family: var(--body-font-family) !important;
    border-radius: 8px !important;
}

.chat-header {
    background: var(--accent-color) !important;
    color: var(--white) !important;
}

.chat-message-user {
    background: var(--accent-color) !important;
    color: var(--white) !important;
}

.chat-message-bot {
    background: var(--lightAccent-color) !important;
    color: var(--darkAccent-color) !important;
}

/* Animation adjustments */
#ai-chat-widget {
    transition: all 0.3s ease !important;
}

#ai-chat-widget:hover {
    transform: scale(1.05) !important;
}
```

---

## Conditional Loading

### Show/Hide Based on Page Type

```html
<script>
(function() {
    var shouldShowChat = true;
    
    // Hide on gallery pages
    if (document.body.classList.contains('collection-type-gallery')) {
        shouldShowChat = false;
    }
    
    // Hide on cover pages
    if (document.body.classList.contains('collection-type-cover-page')) {
        shouldShowChat = false;
    }
    
    // Only show on specific collections
    var allowedCollections = ['homepage', 'about', 'contact', 'services'];
    var currentCollection = document.body.className.match(/collection-([^\s]+)/);
    if (currentCollection && allowedCollections.indexOf(currentCollection[1]) === -1) {
        shouldShowChat = false;
    }
    
    if (shouldShowChat) {
        var script = document.createElement('script');
        script.async = true;
        script.src = 'https://your-chat-script-url';
        script.setAttribute('project-id', 'your-unique-chat-id');
        document.body.appendChild(script);
    }
})();
</script>
```

### Business Hours Integration

```html
<script>
(function() {
    var now = new Date();
    var hour = now.getHours();
    var day = now.getDay(); // 0 = Sunday, 6 = Saturday
    
    var isBusinessHours = false;
    
    // Monday to Friday, 9 AM to 5 PM
    if (day >= 1 && day <= 5 && hour >= 9 && hour < 17) {
        isBusinessHours = true;
    }
    
    var script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-chat-script-url';
    script.setAttribute('project-id', 'your-unique-chat-id');
        businessHours: isBusinessHours,
        welcomeMessage: isBusinessHours ? 
            "Hi! We're online and ready to help." : 
            "Hi! We're currently offline but will respond soon."
    }));
    
    document.body.appendChild(script);
})();
</script>
```

---

## Performance Optimization

### Lazy Loading

```html
<script>
(function() {
    var chatLoaded = false;
    
    function loadChat() {
        if (chatLoaded) return;
        chatLoaded = true;
        
        var script = document.createElement('script');
        script.async = true;
        script.src = 'https://your-chat-script-url';
        script.setAttribute('project-id', 'your-unique-chat-id');
        document.body.appendChild(script);
    }
    
    // Load after Squarespace's AJAX loading completes
    if (window.Squarespace && window.Squarespace.onInitialize) {
        window.Squarespace.onInitialize(Y, function() {
            setTimeout(loadChat, 1000);
        });
    } else {
        // Fallback for non-AJAX templates
        window.addEventListener('load', function() {
            setTimeout(loadChat, 1000);
        });
    }
    
    // Also load on user interaction
    document.addEventListener('click', loadChat, { once: true });
    document.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            loadChat();
        }
    }, { once: true });
})();
</script>
```

### Preload for Better Performance

```html
<!-- Add to Header Code Injection -->
<link rel="preload" href="https://your-chat-script-url" as="script">
```

---

## Analytics Integration

### Squarespace Analytics

```html
<script>
// Track chat interactions with Squarespace Analytics
window.addEventListener('chat_event', function(e) {
    // Send to Squarespace Analytics if available
    if (window.Y && window.Y.Squarespace && window.Y.Squarespace.Analytics) {
        window.Y.Squarespace.Analytics.track('Chat Interaction', {
            action: e.detail.action,
            page: window.location.pathname
        });
    }
    
    // Send to Google Analytics if available
    if (typeof gtag !== 'undefined') {
        gtag('event', 'chat_interaction', {
            'chat_action': e.detail.action,
            'page_location': window.location.href
        });
    }
});
</script>
```

---

## Troubleshooting

### Common Squarespace Issues

**Chat Not Appearing**
- Check code injection is saved properly
- Verify chat is enabled in your dashboard
- Clear browser cache and check again
- Test on different pages

**AJAX Loading Issues**
- Some Squarespace templates use AJAX loading
- Chat may need to reload on page transitions
- Use the lazy loading script above

**Mobile Issues**
- Test on actual mobile devices
- Check mobile-specific CSS
- Verify touch interactions work

**Template Conflicts**
- Some templates may have conflicting CSS
- Add `!important` to chat widget styles
- Test with different templates

### Performance Issues

**Slow Loading**
- Use lazy loading techniques
- Preload the chat script
- Optimize for mobile devices

**AJAX Conflicts**
- Chat may disappear on page transitions
- Implement proper AJAX handling
- Consider using code blocks instead

---

## Advanced Features

### Member Area Integration

```html
<script>
(function() {
    var memberData = {};
    
    // Check if user is logged in to member area
    if (document.body.classList.contains('authenticated-account')) {
        memberData.authenticated = true;
        
        // Get member info if available
        var memberName = document.querySelector('.user-accounts-text .display-name');
        if (memberName) {
            memberData.name = memberName.textContent.trim();
        }
    }
    
    var script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-chat-script-url';
    script.setAttribute('project-id', 'your-unique-chat-id');
    
    if (Object.keys(memberData).length > 0) {
            user: memberData
        }));
    }
    
    document.body.appendChild(script);
})();
</script>
```

### Form Integration

```html
<script>
// Track form submissions
document.addEventListener('submit', function(e) {
    if (e.target.classList.contains('sqs-form')) {
        // Send form context to chat
        if (window.chatAPI) {
            window.chatAPI.setContext({
                formSubmitted: true,
                formType: e.target.getAttribute('data-form-id') || 'contact'
            });
        }
    }
});
</script>
```

---