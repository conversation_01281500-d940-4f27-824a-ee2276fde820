// Widget entry file
import { ChatWidget } from "./ChatWidget";
import { getWidgetConfig } from "../config";

// Global type declarations
declare global {
  interface Window {
    ChatAssistantConfig?: {
      projectId?: string;
      assistantName?: string;
      welcomeMessage?: string;
      color?: string;
      logo?: string | null;
      suggestedQuestions?: string[];
      apiUrl?: string;
    };
    ChatAssistantWidget?: ChatWidget;
  }
}

// 获取项目配置的函数
async function getProjectConfig(projectId: string) {
  try {
    const widgetConfig = getWidgetConfig();
    const response = await fetch(
      `${widgetConfig.api.projectsEndpoint}/${projectId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    return data;
  } catch (error) {
    console.error("Failed to fetch project config:", error);
    // 返回默认配置
    return {
      id: projectId,
      name: "AI Assistant",
      settings: {
        name: "AI Assistant",
        welcomeMsg: "Hello! I'm your AI assistant. How can I help you today?",
        color: "#3B82F6",
        logo: "",
        suggestedEnable: true,
        suggestedQuestions: [
          "I want to learn about product information",
          "How to contact customer service",
          "Are there any promotional activities",
        ],
      },
    };
  }
}

// 初始化聊天组件
async function initChatWidget() {
  // 检查是否已经初始化
  if (
    document.getElementById("ai-chat-widget") ||
    document.getElementById("ai-chat-toggle")
  ) {
    return;
  }

  let projectId: string | undefined;

  // 从脚本属性获取项目ID
  const currentScript =
    document.currentScript ||
    ((function () {
      const scripts = document.getElementsByTagName("script");
      return scripts[scripts.length - 1];
    })() as HTMLScriptElement);

  projectId = currentScript?.getAttribute("project-id") || undefined;

  if (!projectId) {
    console.error(
      "Chat Widget: project-id attribute or ChatAssistantConfig is required"
    );
    return;
  }

  try {
    // 获取项目配置
    const projectConfig = projectId ? await getProjectConfig(projectId) : null;
    const setting = projectConfig?.settings;

    // 获取默认配置
    const wd = getWidgetConfig().widget;

    // 合并配置（优先级：用户配置 > 项目配置 > 默认配置）
    const widgetOptions = {
      projectId,
      assistantName: setting?.name || wd.defaultAssistantName,
      welcomeMessage: setting?.welcomeMsg || wd.defaultWelcomeMessage,
      color: setting?.color || wd.defaultColor,
      logo: setting?.logo || null,
      suggestedQuestions:
        setting?.suggestedEnable == true
          ? setting?.suggestedQuestions || wd.defaultSuggestedQuestions
          : [],
    };

    // 创建聊天组件
    const chatWidget = new ChatWidget(widgetOptions);

    // 将实例保存到全局变量，方便外部访问
    window.ChatAssistantWidget = chatWidget;
  } catch (error) {
    console.error("Chat Widget: Failed to initialize", error);
  }
}

// 等待DOM加载完成后初始化
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", initChatWidget);
} else {
  initChatWidget();
}

// 导出用于构建
export { ChatWidget, initChatWidget };
