# WebView Installation

Easily add the AI-powered Web Chat to your mobile apps using WebView. Follow these quick steps to start chatting with users in no time!

## Overview

WebView integration allows you to:
- Add chat functionality to Android and iOS apps
- Maintain consistent chat experience across platforms
- Leverage existing web chat configuration
- Provide seamless user support within your mobile app

## Prerequisites

- Your unique Project ID from the dashboard
- Basic knowledge of Android/iOS development
- WebView component in your mobile app

---

## Android Integration

### Step 1: Add WebView to Layout

Add the WebView component to your layout XML file:

```xml
<WebView
    android:id="@+id/webview"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="16dp" />
```

### Step 2: Initialize WebView in Activity

Configure the WebView in your Activity or Fragment:

```kotlin
val webView: WebView = findViewById(R.id.webview)
webView.settings.javaScriptEnabled = true
webView.settings.domStorageEnabled = true
webView.settings.allowContentAccess = true

val chatScript = """
<html>
<head></head>
<body>
    <script async src="https://your-chat-script-url" 
        project-id="your-project-id"></script>
</body>
</html>
"""

webView.loadDataWithBaseURL(null, chatScript, "text/html", "UTF-8", null)
```

### Step 3: Handle WebView Navigation (Optional)

```kotlin
webView.webViewClient = WebViewClient()
```

**Reference:** [Android WebView Documentation](https://developer.android.com/reference/android/webkit/WebView)

---

## iOS Integration

### Step 1: Add WKWebView

Import WebKit and create the WebView:

```swift
import WebKit

let webView = WKWebView(frame: view.bounds)
webView.configuration.preferences.javaScriptEnabled = true
view.addSubview(webView)
```

### Step 2: Load Chat Script

Load the chat script into the WebView:

```swift
let htmlString = """
<html>
<head></head>
<body>
    <script async src="https://your-chat-script-url" 
        project-id="your-project-id"></script>
</body>
</html>
"""

webView.loadHTMLString(htmlString, baseURL: nil)
```

### Step 3: Handle Navigation (Optional)

```swift
webView.navigationDelegate = self
```

**Reference:** [iOS WKWebView Documentation](https://developer.apple.com/documentation/webkit/wkwebview)

---

## Advanced Configuration

### Full-Screen Chat Integration

For a full-screen chat experience:

```kotlin
// Android - Full screen WebView
val chatScript = """
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { margin: 0; padding: 0; }
        #ai-chat-widget { 
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            border: none !important;
        }
    </style>
</head>
<body>
    <script async src="https://your-chat-script-url" 
        project-id="your-project-id"></script>
</body>
</html>
"""
```

```swift
// iOS - Full screen WebView
let htmlString = """
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { margin: 0; padding: 0; }
        #ai-chat-widget { 
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            border: none !important;
        }
    </style>
</head>
<body>
    <script async src="https://your-chat-script-url"
        project-id="your-project-id"></script>
</body>
</html>
"""
```

### User Context Integration

Pass user information to the chat:

```kotlin
// Android - With user context
val userEmail = "<EMAIL>"
val userId = "user123"

val chatScript = """
<html>
<head></head>
<body>
    <script async src="https://your-chat-script-url"
        project-id="your-project-id"></script>
</body>
</html>
"""
```

```swift
// iOS - With user context
let userEmail = "<EMAIL>"
let userId = "user123"

let htmlString = """
<html>
<head></head>
<body>
    <script async src="https://your-chat-script-url"
        project-id="your-project-id"></script>
</body>
</html>
"""
```

---

## Performance Optimization

### Android Optimization

```kotlin
// Enable hardware acceleration
webView.setLayerType(View.LAYER_TYPE_HARDWARE, null)

// Optimize caching
webView.settings.cacheMode = WebSettings.LOAD_DEFAULT
webView.settings.setAppCacheEnabled(true)

// Optimize loading
webView.settings.loadsImagesAutomatically = true
webView.settings.blockNetworkImage = false
```

### iOS Optimization

```swift
// Configure for better performance
let configuration = WKWebViewConfiguration()
configuration.preferences.javaScriptEnabled = true
configuration.allowsInlineMediaPlayback = true

let webView = WKWebView(frame: view.bounds, configuration: configuration)
```

---

## Troubleshooting

### Common Android Issues

**JavaScript Not Working**
- Ensure `javaScriptEnabled = true`
- Check `domStorageEnabled = true`
- Verify network permissions in manifest

**WebView Not Loading**
- Check internet connectivity
- Verify the script URL is accessible
- Test with a simple HTML page first

### Common iOS Issues

**Content Not Displaying**
- Ensure JavaScript is enabled in configuration
- Check App Transport Security settings
- Verify the HTML string is properly formatted

**Navigation Issues**
- Implement `WKNavigationDelegate` if needed
- Handle loading states appropriately
- Check for any URL restrictions

---

## Security Considerations

### Android Security

```xml
<!-- Add to AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET" />

<!-- Optional: Restrict network access -->
<application android:usesCleartextTraffic="false">
```

### iOS Security

```swift
// Configure App Transport Security in Info.plist if needed
// Ensure HTTPS connections for production
```

---

## Testing

### Device Testing

1. **Test on different screen sizes** - Phone, tablet, different orientations
2. **Test network conditions** - WiFi, cellular, offline scenarios
3. **Test user interactions** - Touch, scroll, keyboard input
4. **Test performance** - Memory usage, loading times

### Integration Testing

1. **Verify chat functionality** - Send/receive messages
2. **Test user context** - Ensure user data is passed correctly
3. **Test navigation** - Back button, app lifecycle
4. **Test edge cases** - Network interruption, app backgrounding

---

## Final Steps

1. **Replace the project-id** with your unique Project ID from the dashboard
2. **Test the WebView** in different screen sizes and devices
3. **Optimize for performance** (caching, lazy loading)
4. **Submit to app stores** following platform guidelines

---

## Support

Need help? Contact our support team - we've got your back!

Now your app is ready to connect with users in real-time! 🚀

---