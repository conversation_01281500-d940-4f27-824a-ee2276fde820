# Website Installation

Integrate the AI-powered Web Chat on your website to offer instant support, improving visitor engagement and experience.

## Overview

This method works for:
- Static HTML websites
- Custom-built websites
- Any site where you can edit the HTML code
- Websites hosted on any platform

## Copy Your Installation Code

Log in to your account and go to **Test & Install**. Copy the installation code provided.

---

## Adding the Chat to Your Website

### Step 1: Open Your Website's HTML

Locate the HTML file where you want to add the chatbot.

### Step 2: Insert the Code

Paste the copied installation code just before the closing `</body>` tag.

```html
<script async
  src="https://your-chat-script-url"
  project-id="project-id">
</script>
</body>
</html>
```

### Step 3: Save and Publish Your Site

Save the changes and publish your site to make the chatbot live.