import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PasswordStrengthIndicator } from "@/components/PasswordStrengthIndicator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/context/AuthContext";
import { useTranslation } from "@/hooks/useTranslation";
import { validatePassword } from "@/utils/passwordValidation";
import { Shield, Trash2, User } from "lucide-react";
import { useState, useEffect } from "react";
// import { toast } from "sonner";
import { DeleteAccountDialog } from "@/components/dialogs/DeleteAccountDialog";

export default function Account() {
  const { user, updateProfile, deleteAccount, isLoading: authLoading } = useAuth();
  const { t, language } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [enbleEmailEdit, setEnbleEmailEdit] = useState(false);
  const [profileData, setProfileData] = useState({
    name: user?.name || "",
    email: user?.email || "",
    avatar: user?.avatar || null,
  });
  const [passwordData, setPasswordData] = useState({
    oldPassword: "",
    password: "",
    confirmPassword: "",
  });
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // 监听用户状态变化，更新 profileData
  useEffect(() => {
    if (user) {
      setProfileData({
        name: user.name || "",
        email: user.email || "",
        avatar: user.avatar || null,
      });
    }
  }, [user]);

  const handleProfileUpdate = async () => {
    setIsLoading(true);
    try {
      await updateProfile({ ...user, ...profileData });
      // toast.success("Profile updated successfully");
    } catch (error) {
      // toast.error("Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordChange = async () => {
    // Validate new password strength
    const passwordValidation = validatePassword(
      passwordData.password,
      language
    );
    if (!passwordValidation.isValid) {
      // toast.error(t("passwordTooWeak"));
      return;
    }

    if (passwordData.password !== passwordData.confirmPassword) {
      // toast.error(t("passwordMismatch"));
      return;
    }

    setIsLoading(true);
    try {
      await updateProfile({ ...user, ...passwordData });

      // toast.success("Password updated successfully");
      setPasswordData({
        oldPassword: "",
        password: "",
        confirmPassword: "",
      });
    } catch (error) {
      // toast.error("Failed to update password");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAccount = async (password: string) => {
    setIsLoading(true);
    try {
      await deleteAccount(password);
    } catch (error) {
      throw error; // Re-throw to let dialog handle it
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteDialog(true);
  };

  return (
    <DashboardLayout>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">{t("account")}</h1>
        <p className="text-gray-600 mt-2">{t("accountPreferences")}</p>
      </div>

      <Tabs defaultValue="personal" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 max-w-md">
          <TabsTrigger value="personal" className="flex items-center gap-2">
            <User className="w-4 h-4" />
            {t("personal")}
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="w-4 h-4" />
            {t("security")}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="personal" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t("personalInformation")}</CardTitle>
              <CardDescription>
                {t("updatePersonalDetails")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Name Field */}
              <div className="space-y-2">
                <Label htmlFor="name">{t("name")}</Label>
                <Input
                  id="name"
                  value={profileData.name}
                  onChange={(e) =>
                    setProfileData((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  placeholder={t("enterYourName")}
                />
              </div>

              {/* Profile Picture */}
              {/* <div className="space-y-4">
                <Label>Profile picture</Label>
                <div className="flex items-center gap-6">
                  <Avatar className="w-20 h-20">
                    <AvatarImage src={profileData.avatar || undefined} />
                    <AvatarFallback className="bg-blue-500 text-white text-lg">
                      {getInitials(profileData.name || "User")}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex-1">
                    <div
                      className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 transition-colors"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        SVG, PNG, JPG or GIF (max. 1000x1000 px)
                      </p>
                    </div>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleAvatarUpload}
                      className="hidden"
                    />
                  </div>
                </div>
              </div> */}

              {/* Email Field */}
              <div className="space-y-2">
                <Label htmlFor="email">{t("emailAddress")}</Label>
                <div className="flex items-center gap-3">
                  <Input
                    id="email"
                    type="email"
                    value={profileData.email}
                    onChange={(e) =>
                      setProfileData((prev) => ({
                        ...prev,
                        email: e.target.value,
                      }))
                    }
                    placeholder={t("enterYourEmail")}
                    className="flex-1"
                    disabled={!enbleEmailEdit}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEnbleEmailEdit(true)}
                  >
                    {t("editButton")}
                  </Button>
                </div>
              </div>

              {/* Save Button */}
              <div className="flex justify-end">
                <Button
                  onClick={handleProfileUpdate}
                  disabled={isLoading}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isLoading ? t("saving") : t("save")}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t("securitySettings")}</CardTitle>
              <CardDescription>
                {t("managePasswordSecurity")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Current Password */}
              <div className="space-y-2">
                <Label htmlFor="current-password">{t("currentPassword")}</Label>
                <Input
                  id="current-password"
                  type="password"
                  value={passwordData.oldPassword}
                  onChange={(e) =>
                    setPasswordData((prev) => ({
                      ...prev,
                      oldPassword: e.target.value,
                    }))
                  }
                  placeholder={t("enterCurrentPassword")}
                />
              </div>

              {/* New Password */}
              <div className="space-y-2">
                <Label htmlFor="new-password">{t("newPassword")}</Label>
                <Input
                  id="new-password"
                  type="password"
                  value={passwordData.password}
                  onChange={(e) =>
                    setPasswordData((prev) => ({
                      ...prev,
                      password: e.target.value,
                    }))
                  }
                  placeholder={t("enterNewPassword")}
                />
                <PasswordStrengthIndicator password={passwordData.password} />
              </div>

              {/* Confirm Password */}
              <div className="space-y-2">
                <Label htmlFor="confirm-password">{t("confirmNewPassword")}</Label>
                <Input
                  id="confirm-password"
                  type="password"
                  value={passwordData.confirmPassword}
                  onChange={(e) =>
                    setPasswordData((prev) => ({
                      ...prev,
                      confirmPassword: e.target.value,
                    }))
                  }
                  placeholder={t("confirmNewPasswordPlaceholder")}
                />
              </div>

              {/* Update Password Button */}
              <div className="flex justify-end">
                <Button
                  onClick={handlePasswordChange}
                  disabled={
                    isLoading ||
                    !passwordData.oldPassword ||
                    !passwordData.password
                  }
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isLoading ? t("updating") : t("updatePassword")}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Delete Account Section */}
          <Card className="border-red-200">
            <CardHeader>
              <CardTitle className="text-red-600">{t("dangerZone")}</CardTitle>
              <CardDescription>
                {t("permanentlyDeleteAccount")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-red-600">{t("deleteAccount")}</Label>
                  <p className="text-sm text-gray-600">
                    {t("deleteAccountWarning")}
                  </p>
                </div>
                <Button
                  variant="destructive"
                  onClick={handleDeleteClick}
                  disabled={isLoading}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="w-4 h-4" />
                  {t("deleteAccountButton")}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Delete Account Confirmation Dialog */}
      <DeleteAccountDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleDeleteAccount}
        isLoading={isLoading}
      />
    </DashboardLayout>
  );
}
