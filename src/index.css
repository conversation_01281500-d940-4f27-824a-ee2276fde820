@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 210 40% 98%;
    --primary-glow: 217.2 91.2% 70%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    /* Enhanced sidebar colors */
    --sidebar-background: 240 6% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 217.2 91.2% 97%;
    --sidebar-accent-foreground: 217.2 91.2% 45%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --sidebar-muted: 240 4.8% 95.9%;
    --sidebar-hover: 217.2 91.2% 94%;
    
    /* Gradients for enhanced visual appeal */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-sidebar: linear-gradient(180deg, hsl(var(--sidebar-background)), hsl(240 6% 96%));
    
    /* Shadows for depth */
    --shadow-elegant: 0 4px 6px -1px hsl(var(--primary) / 0.1), 0 2px 4px -1px hsl(var(--primary) / 0.06);
    --shadow-subtle: 0 1px 3px 0 hsl(240 5% 64% / 0.1), 0 1px 2px 0 hsl(240 5% 64% / 0.06);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --primary-glow: 217.2 91.2% 45%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    
    /* Enhanced dark sidebar colors */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 240 5.9% 10%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 217.2 91.2% 75%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --sidebar-muted: 240 3.7% 15.9%;
    --sidebar-hover: 240 3.7% 18%;
    
    /* Dark gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-sidebar: linear-gradient(180deg, hsl(var(--sidebar-background)), hsl(240 5.9% 8%));
    
    /* Dark shadows */
    --shadow-elegant: 0 4px 6px -1px hsl(0 0% 0% / 0.3), 0 2px 4px -1px hsl(0 0% 0% / 0.2);
    --shadow-subtle: 0 1px 3px 0 hsl(0 0% 0% / 0.3), 0 1px 2px 0 hsl(0 0% 0% / 0.2);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Enhanced sidebar animations and styles */
  .sidebar-menu-item {
    @apply transition-all duration-200 ease-in-out hover:scale-[1.02];
  }
  
  .sidebar-menu-item:hover {
    box-shadow: var(--shadow-subtle);
  }
  
  .sidebar-brand {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .sidebar-icon-glow {
    filter: drop-shadow(0 0 4px hsl(var(--primary) / 0.3));
  }
  
  .sidebar-divider {
    background: linear-gradient(90deg, transparent, hsl(var(--sidebar-border)), transparent);
    height: 1px;
    margin: 0.75rem 0;
  }
}