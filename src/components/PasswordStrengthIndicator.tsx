import React from 'react';
import { Check, X } from 'lucide-react';
import { passwordRequirements, validatePassword, getPasswordStrengthColor, getPasswordStrengthText } from '@/utils/passwordValidation';
import { useTranslation } from '@/hooks/useTranslation';

interface PasswordStrengthIndicatorProps {
  password: string;
  showRequirements?: boolean;
}

export const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
  showRequirements = true
}) => {
  const { language } = useTranslation();
  const validation = validatePassword(password, language);

  if (!password) {
    return null;
  }

  return (
    <div className="mt-2 space-y-2">
      {/* Password Strength Bar */}
      <div className="space-y-1">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">
            {language === 'zh' ? '密码强度' : 'Password Strength'}
          </span>
          <span className={getPasswordStrengthColor(validation.strength)}>
            {getPasswordStrengthText(validation.strength, language)}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              validation.strength === 'weak'
                ? 'bg-red-500 w-1/3'
                : validation.strength === 'medium'
                ? 'bg-yellow-500 w-2/3'
                : 'bg-green-500 w-full'
            }`}
          />
        </div>
      </div>

      {/* Password Requirements */}
      {showRequirements && (
        <div className="space-y-1">
          <p className="text-sm text-gray-600 font-medium">
            {language === 'zh' ? '密码要求：' : 'Password Requirements:'}
          </p>
          <ul className="space-y-1">
            {passwordRequirements.map((requirement, index) => {
              const isValid = requirement.test(password);
              return (
                <li key={index} className="flex items-center text-xs">
                  {isValid ? (
                    <Check className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                  ) : (
                    <X className="h-3 w-3 text-red-500 mr-2 flex-shrink-0" />
                  )}
                  <span className={isValid ? 'text-green-600' : 'text-red-600'}>
                    {language === 'zh' ? requirement.messageZh : requirement.message}
                  </span>
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
};
