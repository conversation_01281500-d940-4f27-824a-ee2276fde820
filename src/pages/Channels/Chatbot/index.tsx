import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import Project from "@/pages/Channels/Chatbot/components/Project";
import TestInstall from "@/pages/Channels/Chatbot/components/TestInstall";
import { Settings, TestTube } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";

const ChatbotManagement = () => {
  const { t } = useTranslation();

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <Tabs defaultValue="project" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger
              value="project"
              className="flex items-center space-x-2"
            >
              <Settings className="h-4 w-4" />
              <span>{t("general")}</span>
            </TabsTrigger>
            <TabsTrigger
              value="test-install"
              className="flex items-center space-x-2"
            >
              <TestTube className="h-4 w-4" />
              <span>{t("testAndInstall")}</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="project" className="mt-6">
            <div className="bg-white rounded-lg">
              <Project />
            </div>
          </TabsContent>

          <TabsContent value="test-install" className="mt-6">
            <div className="bg-white rounded-lg">
              <TestInstall />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default ChatbotManagement;
