FROM node:18.19.0-alpine3.17 as build

RUN apk add --no-cache git

WORKDIR /app

# Install dependencies
COPY package.json package.json
COPY package-lock.json package-lock.json

RUN npm ci --unsafe-perm=true

# Build
COPY public public
COPY .env .env
COPY tsconfig.json tsconfig.json
COPY tsconfig.app.json tsconfig.app.json
COPY tsconfig.node.json tsconfig.node.json
COPY index.html index.html
COPY postcss.config.js postcss.config.js
COPY tailwind.config.ts tailwind.config.ts
COPY components.json components.json
COPY vite.config.ts vite.config.ts
COPY eslint.config.js eslint.config.js
COPY src src

RUN npm run build

# Deploy
FROM nginx:stable-alpine

COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
