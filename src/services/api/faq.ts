import store from "store2";

// FAQ相关的请求和响应类型
interface AddFaqRequest {
  projectId: string;
  question: string;
  answer: string;
}

interface UpdateFaqRequest {
  question?: string;
  answer?: string;
}

interface GetFaqsQuery {
  projectId: string;
  keyword?: string;
  pageNumber?: string;
  pageSize?: string;
}

interface FaqResponse {
  id: string;
  projectId: string;
  question: string;
  answer: string;
  createTime: string;
  updateTime: string;
}

interface Page<T> {
  items: T[];
  total: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

// 获取FAQ列表
export const getFaqs = async (
  query: GetFaqsQuery
): Promise<FaqResponse[] | Page<FaqResponse>> => {
  const params = new URLSearchParams({
    projectId: query.projectId,
    ...(query.keyword && { keyword: query.keyword }),
    ...(query.pageNumber && { pageNumber: query.pageNumber }),
    ...(query.pageSize && { pageSize: query.pageSize }),
  });

  const response = await fetch(`/v1/faqs?${params}`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

// 创建FAQ
export const createFaq = async (
  request: AddFaqRequest
): Promise<FaqResponse> => {
  const response = await fetch("/v1/faqs", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

// 更新FAQ
export const updateFaq = async (
  faqId: string,
  request: UpdateFaqRequest
): Promise<FaqResponse> => {
  const response = await fetch(`/v1/faqs/${faqId}`, {
    method: "PATCH",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

// 删除FAQ
export const deleteFaq = async (faqId: string): Promise<void> => {
  const response = await fetch(`/v1/faqs/${faqId}`, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }
};

// 导出类型
export type {
  AddFaqRequest,
  UpdateFaqRequest,
  GetFaqsQuery,
  FaqResponse,
  Page,
};
