import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { useAuth } from "@/context/AuthContext.tsx";
import { useProject } from "@/context/ProjectContext";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import {
  ChatMessage,
  ChatStatistics,
  getChats,
  getSessions,
  SessionResponse,
  statisticsChat,
} from "@/services/api";
import {
  Filter,
  Globe,
  MapPin,
  MessageSquare,
  MoreHorizontal,
  Search,
  ThumbsDown,
  ThumbsUp,
  User,
} from "lucide-react";
import { useEffect, useState } from "react";
import ReactMarkdown from "react-markdown";
import { useNavigate } from "react-router-dom";

const ChatRecords = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const { currentProject } = useProject();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedConversation, setSelectedConversation] = useState<
    string | null
  >(null);
  const [sessions, setSessions] = useState<SessionResponse[]>([]);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [loadingSessions, setLoadingSessions] = useState(false);
  const [loadingChats, setLoadingChats] = useState(false);

  // 获取会话列表
  const fetchSessions = async () => {
    if (!currentProject?.id) return;

    setLoadingSessions(true);
    try {
      const sessionList = await getSessions({
        projectId: currentProject.id,
        pageSize: 50,
      });
      setSessions(sessionList);

      // 默认选中第一个会话
      if (sessionList.length > 0 && !selectedConversation) {
        setSelectedConversation(sessionList[0].id);
      }
    } catch (error) {
      console.error("Failed to fetch sessions:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: t("fetchConversationFailed"),
      });
    } finally {
      setLoadingSessions(false);
    }
  };

  // 获取聊天记录
  const fetchChats = async (sessionId: string) => {
    setLoadingChats(true);
    try {
      const chatList = await getChats({
        sessionId,
        pageSize: 100,
      });
      setChatMessages(chatList);
    } catch (error) {
      console.error("Failed to fetch chats:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: t("fetchChatsFailed"),
      });
    } finally {
      setLoadingChats(false);
    }
  };

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading, navigate]);

  useEffect(() => {
    if (currentProject?.id) {
      fetchSessions();
      loadStatistics();
    }
  }, [currentProject?.id]);

  useEffect(() => {
    if (selectedConversation) {
      fetchChats(selectedConversation);
    }
  }, [selectedConversation]);

  // 过滤会话
  const filteredSessions = sessions.filter((session) =>
    session.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 获取当前选中的会话
  const currentSession = sessions.find(
    (session) => session.id === selectedConversation
  );

  const [chatStatistics, setChatStatistics] = useState<ChatStatistics>({
    totalChats: 0,
    totalCity: 0,
    totalConversations: 0,
    totalCountry: 0,
  });

  function loadStatistics() {
    if (currentProject?.id) {
      // 设置开始时间为2024年1月1日
      const startTime = new Date();
      startTime.setFullYear(2024, 0, 1);
      const endTime = new Date();
      statisticsChat(
        currentProject.id,
        startTime.toISOString(),
        endTime.toISOString()
      ).then((data) => {
        setChatStatistics(data);
      });
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <MessageSquare className="mr-2 h-6 w-6" />
              {t("chatRecords")}
            </h1>
            <p className="text-gray-600 mt-1">{t("viewChatRecords")}</p>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {t("totalConversations")}
              </CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{chatStatistics.totalConversations}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {t("totalChats")}
              </CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{chatStatistics.totalChats}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {t("totalCountry")}
              </CardTitle>
              <Globe className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{chatStatistics.totalCountry}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {t("totalCity")}
              </CardTitle>
              <MapPin className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{chatStatistics.totalCity}</div>
            </CardContent>
          </Card>
        </div>

        {/* 聊天记录主界面 */}
        <div className="grid grid-cols-12 gap-6 h-[600px]">
          {/* 左侧对话列表 */}
          <div className="col-span-4">
            <Card className="h-full">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center">
                    <MessageSquare className="mr-2 h-5 w-5" />
                    {t("allConversations")}
                  </CardTitle>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>

                {/* 搜索框 */}
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder={t("search")}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-2"
                  >
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="p-0">
                <ScrollArea className="h-[500px]">
                  {loadingSessions ? (
                    <div className="p-4 text-center text-gray-500">
                      {t("loading")}
                    </div>
                  ) : filteredSessions.length === 0 ? (
                    <div className="p-4 text-center text-gray-500">
                      {t("noConversation")}
                    </div>
                  ) : (
                    filteredSessions.map((session) => (
                      <div
                        key={session.id}
                        className={`p-4 border-b cursor-pointer hover:bg-gray-50 ${
                          selectedConversation === session.id
                            ? "bg-blue-50 border-r-2 border-r-blue-500"
                            : ""
                        }`}
                        onClick={() => setSelectedConversation(session.id)}
                      >
                        <div className="flex items-start space-x-3">
                          <Avatar className="w-8 h-8 flex-shrink-0">
                            <AvatarFallback className="bg-blue-500 text-white text-sm">
                              {session.name.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <h4 className="text-sm font-medium text-gray-900 truncate">
                                {session.name}
                              </h4>
                              <Badge
                                variant={
                                  session.details?.channel === "WebChat"
                                    ? "default"
                                    : "secondary"
                                }
                                className="text-xs"
                              >
                                {session.details?.channel || "WebChat"}
                              </Badge>
                            </div>

                            <div className="flex items-center justify-between mt-2">
                              <span className="text-xs text-gray-500">
                                {new Date(session.createTime).toLocaleString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* 右侧对话详情 */}
          <div className="col-span-8">
            <Card className="h-full">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">
                    {currentSession
                      ? currentSession.name
                      : t("selectConversation")}
                  </CardTitle>
                  {currentSession && (
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant={
                          currentSession.details?.status === "Open"
                            ? "default"
                            : "secondary"
                        }
                      >
                        {currentSession.details?.status || "Unknown"}
                      </Badge>
                      <Badge variant="outline">
                        {currentSession.details?.channel || "Unknown"}
                      </Badge>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
                {currentSession && (
                  <p className="text-sm text-gray-500">
                    {new Date(currentSession.createTime).toLocaleString()}
                  </p>
                )}
              </CardHeader>

              <CardContent className="flex-1">
                <div className="grid grid-cols-3 gap-6 h-full">
                  {/* 对话内容 */}
                  <div className="col-span-2">
                    <ScrollArea className="h-[500px] pr-4">
                      <div className="space-y-4">
                        {loadingChats ? (
                          <div className="text-center text-gray-500 py-8">
                            {t("loadingConversations")}
                          </div>
                        ) : chatMessages.length === 0 ? (
                          <div className="text-center text-gray-500 py-8">
                            {selectedConversation
                              ? t("noConversation")
                              : t("selectConversation")}
                          </div>
                        ) : (
                          chatMessages.map((message, index) => (
                            <div key={index} className="space-y-4">
                              {/* 用户问题 */}
                              <div className="text-right">
                                <div className="inline-block bg-gray-100 rounded-lg px-4 py-2 max-w-[80%]">
                                  <p className="text-sm text-gray-800">
                                    {message.question}
                                  </p>
                                  <p className="text-xs text-gray-500 mt-1">
                                    {new Date(
                                      message.createTime
                                    ).toLocaleString()}
                                  </p>
                                </div>
                              </div>

                              {/* AI回答 */}
                              <div>
                                <div className="bg-blue-50 rounded-lg p-4 max-w-[90%]">
                                  <div className="text-sm text-gray-800 mb-3">
                                    <ReactMarkdown>
                                      {String(message.answer || "")}
                                    </ReactMarkdown>
                                  </div>

                                  {message.reference &&
                                    Object.keys(message.reference).length >
                                      0 && (
                                      <div className="border-t pt-3">
                                        <p className="text-xs text-gray-500 mb-2">
                                          {t("sources")}
                                        </p>
                                        <div className="text-xs text-blue-600">
                                          {t("referenceData")}
                                        </div>
                                      </div>
                                    )}

                                  {/* 点赞/点踩 */}
                                  <div className="flex items-center space-x-2 mt-3 pt-2 border-t">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className={`h-6 px-2 ${
                                        message.like === 1
                                          ? "text-green-600"
                                          : "text-gray-400"
                                      }`}
                                    >
                                      <ThumbsUp className="h-3 w-3" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className={`h-6 px-2 ${
                                        message.like === 0
                                          ? "text-red-600"
                                          : "text-gray-400"
                                      }`}
                                    >
                                      <ThumbsDown className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                    </ScrollArea>
                  </div>

                  {/* 对话详情面板 */}
                  <div className="col-span-1 border-l pl-4">
                    {currentSession ? (
                      <Tabs defaultValue="detail" className="w-full">
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger value="summary">{t("summary")}</TabsTrigger>
                          <TabsTrigger value="detail">{t("detail")}</TabsTrigger>
                        </TabsList>
                        
                        <TabsContent value="summary" className="mt-4">
                          <div className="text-center py-8 text-gray-500">
                            {currentSession.summary || t("summaryContent")}
                          </div>
                        </TabsContent>
                        
                        <TabsContent value="detail" className="mt-4">
                          <div className="space-y-4">
                            <div>
                              <h3 className="text-sm font-medium text-gray-700 mb-2">
                                {t("conversationDetails")}
                              </h3>
                            </div>

                            <div className="space-y-3 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-500">{t("status")}</span>
                                <span className="text-gray-900">
                                  {currentSession.details?.status || t("unknown")}
                                </span>
                              </div>

                              <div className="flex justify-between">
                                <span className="text-gray-500">{t("id")}</span>
                                <span className="text-gray-900 text-xs">
                                  {currentSession.id}
                                </span>
                              </div>

                              <div className="flex justify-between">
                                <span className="text-gray-500">{t("channel")}</span>
                                <div className="flex items-center">
                                  <Globe className="w-3 h-3 mr-1" />
                                  <span className="text-gray-900">
                                    {currentSession.details?.channel || t("unknown")}
                                  </span>
                                </div>
                              </div>
                            </div>

                            <div className="border-t pt-4">
                              <h4 className="text-sm font-medium text-gray-700 mb-3">
                                {t("customerInfo")}
                              </h4>

                              <div className="space-y-3 text-sm">
                                <div className="flex justify-between">
                                  <span className="text-gray-500">{t("country")}</span>
                                  <span className="text-gray-900">
                                    {currentSession.customerInfo?.country ||
                                      "Unknown"}
                                  </span>
                                </div>

                                <div className="flex justify-between">
                                  <span className="text-gray-500">{t("city")}</span>
                                  <span className="text-gray-900">
                                    {currentSession.customerInfo?.city || "Unknown"}
                                  </span>
                                </div>

                                <div className="flex justify-between">
                                  <span className="text-gray-500">{t("browser")}</span>
                                  <span className="text-gray-900">
                                    {currentSession.customerInfo?.browser ||
                                      "Unknown"}
                                  </span>
                                </div>

                                <div className="flex justify-between">
                                  <span className="text-gray-500">{t("system")}</span>
                                  <span className="text-gray-900">
                                    {currentSession.customerInfo?.system ||
                                      "Unknown"}
                                  </span>
                                </div>

                                <div className="flex justify-between">
                                  <span className="text-gray-500">{t("createdAt")}</span>
                                  <span className="text-gray-900 text-xs">
                                    {new Date(
                                      currentSession.createTime
                                    ).toLocaleString()}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </TabsContent>
                      </Tabs>
                    ) : (
                      <div className="text-center text-gray-500 py-8">
                        {t("selectConversation")}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ChatRecords;
