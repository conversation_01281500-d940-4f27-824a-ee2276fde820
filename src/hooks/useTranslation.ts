
import { useState, useEffect } from 'react';
import { translations, Language, TranslationKey } from '../i18n/translations';

// 重载函数类型定义
interface TranslationFunction {
  (key: TranslationKey): string;
  (key: TranslationKey, params: Record<string, string | number>): string;
}

export const useTranslation = () => {
  const [language, setLanguage] = useState<Language>(() => {
    // Initialize from localStorage or default to 'en'
    const savedLang = localStorage.getItem('language') as Language;
    return (savedLang && translations[savedLang]) ? savedLang : 'en';
  });

  const t: TranslationFunction = (key: TranslationKey, params?: Record<string, string | number>): string => {
    let text = translations[language][key] || key;

    // Replace parameters in the text
    if (params) {
      Object.entries(params).forEach(([paramKey, paramValue]) => {
        text = text.replace(new RegExp(`\\{${paramKey}\\}`, 'g'), String(paramValue));
      });
    }

    return text;
  };

  const changeLanguage = (lang: Language) => {
    setLanguage(lang);
    localStorage.setItem('language', lang);
  };

  // Ensure language is persisted when component mounts
  useEffect(() => {
    const savedLang = localStorage.getItem('language') as Language;
    if (savedLang && translations[savedLang] && savedLang !== language) {
      setLanguage(savedLang);
    }
  }, []);

  return { t, language, changeLanguage };
};
