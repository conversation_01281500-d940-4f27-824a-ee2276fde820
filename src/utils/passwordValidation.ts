export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'medium' | 'strong';
}

export interface PasswordRequirement {
  test: (password: string) => boolean;
  message: string;
  messageZh: string;
}

export const passwordRequirements: PasswordRequirement[] = [
  {
    test: (password: string) => password.length >= 8,
    message: 'At least 8 characters long',
    messageZh: '至少8个字符'
  },
  {
    test: (password: string) => /[a-z]/.test(password),
    message: 'Contains at least one lowercase letter',
    messageZh: '包含至少一个小写字母'
  },
  {
    test: (password: string) => /[A-Z]/.test(password),
    message: 'Contains at least one uppercase letter',
    messageZh: '包含至少一个大写字母'
  },
  {
    test: (password: string) => /\d/.test(password),
    message: 'Contains at least one number',
    messageZh: '包含至少一个数字'
  },
  {
    test: (password: string) => /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
    message: 'Contains at least one special character',
    messageZh: '包含至少一个特殊字符'
  }
];

export const validatePassword = (password: string, language: 'en' | 'zh' = 'en'): PasswordValidationResult => {
  const errors: string[] = [];
  
  passwordRequirements.forEach(requirement => {
    if (!requirement.test(password)) {
      errors.push(language === 'zh' ? requirement.messageZh : requirement.message);
    }
  });

  // Calculate password strength
  let strength: 'weak' | 'medium' | 'strong' = 'weak';
  const passedRequirements = passwordRequirements.filter(req => req.test(password)).length;
  
  if (passedRequirements >= 5) {
    strength = 'strong';
  } else if (passedRequirements >= 3) {
    strength = 'medium';
  }

  return {
    isValid: errors.length === 0,
    errors,
    strength
  };
};

export const getPasswordStrengthColor = (strength: 'weak' | 'medium' | 'strong'): string => {
  switch (strength) {
    case 'weak':
      return 'text-red-500';
    case 'medium':
      return 'text-yellow-500';
    case 'strong':
      return 'text-green-500';
    default:
      return 'text-gray-500';
  }
};

export const getPasswordStrengthText = (strength: 'weak' | 'medium' | 'strong', language: 'en' | 'zh' = 'en'): string => {
  const texts = {
    en: {
      weak: 'Weak',
      medium: 'Medium',
      strong: 'Strong'
    },
    zh: {
      weak: '弱',
      medium: '中等',
      strong: '强'
    }
  };
  
  return texts[language][strength];
};
