// 聊天组件的核心逻辑，可以在React和纯JavaScript环境中使用

import {
  getUserSystemInfo,
  type UserSystemInfo,
} from "@/components/ChatRenderer/userInfo";
import { formatToUUID } from "@/utils/common.ts";
import { toast } from "sonner";
import {
  translations,
  type Language,
  type TranslationKey,
} from "@/i18n/translations";

export interface ChatMessage {
  id: number;
  type: "user" | "bot";
  message: string;
  time: string;
}

export interface ChatConfig {
  projectId?: string;
  assistantName: string;
  welcomeMessage: string;
  color: string;
  logo?: string | null;
  suggestedQuestions: string[];
  sessionApiUrl?: string;
  chatApiUrl: string;
  sessionId?: string;
  language?: Language;
  notCreateSession?: boolean;
}

export interface ChatCoreCallbacks {
  onMessageUpdate: (messages: ChatMessage[]) => void;
  onLoadingChange: (isLoading: boolean) => void;
  onSessionIdChange?: (sessionId: string) => void;
}

export class ChatCore {
  private messages: ChatMessage[] = [];
  private isLoading = false;
  private sessionId = "";
  private config: ChatConfig;
  private callbacks: ChatCoreCallbacks;
  private userSystemInfo: UserSystemInfo | null = null;
  private language: Language;

  constructor(config: ChatConfig, callbacks: ChatCoreCallbacks) {
    this.config = config;
    this.callbacks = callbacks;
    this.language = config.language || this.getDefaultLanguage();

    // 检查是否有存储的会话ID
    this.sessionId = config.sessionId || "";

    // 初始化用户系统信息和会话
    this.initialize();

    // 初始化欢迎消息
    this.addMessage({
      id: 1,
      type: "bot",
      message: config.welcomeMessage,
      time: this.getCurrentTime(),
    });
  }

  private getDefaultLanguage(): Language {
    // Get language from localStorage or browser language
    const savedLang = localStorage.getItem("language") as Language;
    if (savedLang && translations[savedLang]) {
      return savedLang;
    }

    // Fallback to browser language
    const browserLang = navigator.language.toLowerCase();
    if (browserLang.startsWith("zh")) {
      return "zh";
    }
    return "en";
  }

  private t(key: TranslationKey): string {
    return translations[this.language][key] || key;
  }

  private async initialize(): Promise<void> {
    try {
      // 并行初始化用户信息和创建会话
      await this.initializeUserInfo();
      // 只有在 notCreateSession 为 false 时才创建会话
      if (!this.config.notCreateSession) {
        this.ensureSession();
      }
    } catch (error) {
      console.warn("Failed to initialize ChatCore:", error);
    }
  }

  private async ensureSession(): Promise<void> {
    // 如果已经有会话ID，跳过创建
    if (this.sessionId) {
      return;
    }

    try {
      await this.createSession();
    } catch (error) {
      console.warn("Failed to create session during initialization:", error);
    }
  }

  private async initializeUserInfo(): Promise<void> {
    try {
      this.userSystemInfo = await getUserSystemInfo();
    } catch (error) {
      console.warn("Failed to get user system info:", error);
      // Use default values
      this.userSystemInfo = {
        country: this.t("unknown"),
        city: this.t("unknown"),
        browser: this.t("unknown"),
        system: this.t("unknown"),
      };
    }
  }

  private getCurrentTime(): string {
    const locale = this.language === "zh" ? "zh-CN" : "en-US";
    return new Date().toLocaleString(locale, {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  async createSession(): Promise<string> {
    // 如果已经有会话ID，直接返回
    if (this.sessionId) {
      return this.sessionId;
    }

    // 如果没有配置会话API URL，生成一个临时会话ID
    if (!this.config.sessionApiUrl) {
      toast.error(this.t("sessionApiNotConfigured"));
      return "";
    }

    // 确保用户信息已初始化
    if (!this.userSystemInfo) {
      await this.initializeUserInfo();
    }

    return new Promise((resolve, reject) => {
      fetch(this.config.sessionApiUrl!, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          projectId: this.config.projectId,
          name: "consult",
          details: {
            status: "Open",
            channel: "WebChat",
          },
          customerInfo: {
            externalId: this.generateExternalId(),
            country: this.userSystemInfo?.country || this.t("unknown"),
            city: this.userSystemInfo?.city || this.t("unknown"),
            browser: this.userSystemInfo?.browser || this.t("unknown"),
            system: this.userSystemInfo?.system || this.t("unknown"),
          },
        }),
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then((data) => {
          if (data.session_id || data.id) {
            this.sessionId = data.session_id || data.id;

            if (this.callbacks.onSessionIdChange) {
              this.callbacks.onSessionIdChange(this.sessionId);
            }
            resolve(this.sessionId);
          } else {
            reject(new Error(this.t("sessionCreationFailed")));
          }
        })
        .catch((error) => {
          console.error("Failed to create session:", error);
          // 生成临时会话ID
          this.sessionId = this.t("unknown");

          if (this.callbacks.onSessionIdChange) {
            this.callbacks.onSessionIdChange(this.sessionId);
          }

          resolve(this.sessionId);
        });
    });
  }

  private generateExternalId(): string {
    // 生成一个外部ID，用于标识用户
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 11);
    return `web_${timestamp}_${random}`;
  }

  private addMessage(message: ChatMessage) {
    this.messages.push(message);
    this.callbacks.onMessageUpdate([...this.messages]);
  }

  private updateMessage(id: number, updates: Partial<ChatMessage>) {
    this.messages = this.messages.map((msg) =>
      msg.id === id ? { ...msg, ...updates } : msg
    );
    this.callbacks.onMessageUpdate([...this.messages]);
  }

  public async sendMessage(text: string): Promise<void> {
    if (!text.trim() || this.isLoading) return;

    // 添加用户消息
    const userMessage: ChatMessage = {
      id: this.messages.length + 1,
      type: "user",
      message: text,
      time: this.getCurrentTime(),
    };
    this.addMessage(userMessage);

    // 设置加载状态
    this.isLoading = true;
    this.callbacks.onLoadingChange(true);

    // 创建空的机器人消息用于流式更新（时间戳稍后在HTTP响应时设置）
    const botMessageId = this.messages.length + 1;
    const botMessage: ChatMessage = {
      id: botMessageId,
      type: "bot",
      message: "",
      time: "", // 暂时为空，等待HTTP响应时设置
    };
    this.addMessage(botMessage);

    try {
      //判断sessionid - 如果设置了 notCreateSession，则跳过 sessionId 检查
      if (
        this.config.notCreateSession !== true &&
        (!this.sessionId || this.sessionId === "")
      ) {
        this.updateMessage(botMessageId, {
          message: this.t("chatErrorMessage"),
          time: this.getCurrentTime(), //
        });
        this.isLoading = false;
        this.callbacks.onLoadingChange(false);
        return;
      }

      if (this.config.projectId) {
        await this.sendToAPI(text, botMessageId);
      } else {
        // 模拟回复 - 模拟HTTP响应时间
        setTimeout(() => {
          const responseTime = this.getCurrentTime(); // 模拟响应时间
          this.updateMessage(botMessageId, {
            message: this.t("demoResponseMessage"),
            time: responseTime, // 使用响应时间而不是请求时间
          });
          this.isLoading = false;
          this.callbacks.onLoadingChange(false);
        }, 1000);
      }
    } catch (error) {
      console.error("Failed to send message:", error);
      const errorTime = this.getCurrentTime(); // 错误发生时的时间
      this.updateMessage(botMessageId, {
        message: this.t("chatErrorMessage"),
        time: errorTime, // 使用错误发生时的时间
      });
      this.isLoading = false;
      this.callbacks.onLoadingChange(false);
    }
  }

  private async sendToAPI(
    question: string,
    botMessageId: number
  ): Promise<void> {
    const requestBody: any = {
      projectId: this.config.projectId,
      question: question,
    };

    // 如果有会话ID，添加到请求中
    if (this.sessionId) {
      requestBody.sessionId = this.sessionId;
    }

    try {
      const response = await fetch(this.config.chatApiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error("No response body");
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let accumulatedAnswer = "";
      let responseTimeSet = false; // 标记是否已设置响应时间

      //设置已经开始回复，无需显示等待
      this.isLoading = false;
      this.callbacks.onLoadingChange(false);

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split("\n");

        for (const line of lines) {
          if (line.startsWith("data:")) {
            try {
              const jsonStr = line.slice(5);
              const data = JSON.parse(jsonStr);

              if (
                data.code === 0 &&
                data.data &&
                typeof data.data === "object" &&
                data.data.answer
              ) {
                accumulatedAnswer = data.data.answer
                  .replace(/<think>[\s\S]*?<\/think>/g, "")
                  .trim();

                // 在收到第一个响应时设置时间戳为HTTP响应时间
                if (!responseTimeSet) {
                  const responseTime =
                    data.data.timestamp || this.getCurrentTime();
                  this.updateMessage(botMessageId, { time: responseTime });
                  responseTimeSet = true;
                }

                if (data.data.session_id && this.callbacks.onSessionIdChange) {
                  this.sessionId = formatToUUID(data.data.session_id);
                  this.callbacks.onSessionIdChange(this.sessionId);
                }

                this.updateMessage(botMessageId, {
                  message: accumulatedAnswer,
                });
              } else if (data.code === 0 && data.data === true) {
                break;
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } finally {
      this.isLoading = false;
      this.callbacks.onLoadingChange(false);
    }
  }

  public getMessages(): ChatMessage[] {
    return [...this.messages];
  }

  public isLoadingState(): boolean {
    return this.isLoading;
  }

  public getSessionId(): string {
    return this.sessionId;
  }

  public async ensureSessionCreated(): Promise<string> {
    if (!this.sessionId && !this.config.notCreateSession) {
      await this.createSession();
    }
    return this.sessionId;
  }

  public updateConfig(newConfig: Partial<ChatConfig>) {
    this.config = { ...this.config, ...newConfig };

    // 如果语言改变，更新语言设置
    if (newConfig.language) {
      this.language = newConfig.language;
    }

    // 如果欢迎消息改变，更新第一条消息
    if (newConfig.welcomeMessage && this.messages.length > 0) {
      this.updateMessage(1, { message: newConfig.welcomeMessage });
    }
  }

  public updateLanguage(language: Language) {
    this.language = language;
  }
}
