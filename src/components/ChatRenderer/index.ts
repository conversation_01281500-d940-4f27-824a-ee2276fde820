// 通用的聊天组件渲染器
// 可以在 React 和纯 JavaScript 环境中使用

import { ChatMessage } from "./ChatCore";
import { createChatStyles, ChatStyleConfig, icons } from "./ChatStyles";

export interface ChatRendererConfig extends ChatStyleConfig {
  assistantName: string;
  logo?: string | null;
  showInput?: boolean;
  suggestedQuestions?: string[];
  isLoading?: boolean;
  inputValue?: string;
  messages: ChatMessage[];
  onSendMessage?: (message: string) => void;
  onSuggestedQuestion?: (question: string) => void;
  onInputChange?: (value: string) => void;
  onKeyDown?: (event: KeyboardEvent) => void;
  showCloseButton?: boolean;
  onClose?: () => void;
}

export class ChatRenderer {
  private config: ChatRendererConfig;
  private styles: ReturnType<typeof createChatStyles>;

  constructor(config: ChatRendererConfig) {
    this.config = config;
    this.styles = createChatStyles(config);
  }

  // 更新配置
  updateConfig(newConfig: Partial<ChatRendererConfig>) {
    this.config = { ...this.config, ...newConfig };
    this.styles = createChatStyles(this.config);
  }

  // 创建 DOM 元素的通用方法
  private createElement<T extends HTMLElement = HTMLElement>(
    tag: string,
    styles?: Partial<CSSStyleDeclaration>,
    attributes?: Record<string, string | boolean>
  ): T {
    const element = document.createElement(tag) as T;

    if (styles) {
      Object.assign(element.style, styles);
    }

    if (attributes) {
      this.setElementAttributes(element, attributes);
    }

    return element;
  }

  private setElementAttributes(element: HTMLElement, attributes: Record<string, string | boolean>): void {
    Object.entries(attributes).forEach(([key, value]) => {
      if (key === "innerHTML") {
        element.innerHTML = value as string;
      } else if (key === "textContent") {
        element.textContent = value as string;
      } else if (key === "disabled") {
        (element as HTMLInputElement | HTMLButtonElement).disabled = value as boolean;
      } else {
        element.setAttribute(key, String(value));
      }
    });
  }

  // 渲染头部
  private renderHeader(): HTMLElement {
    const header = this.createElement("div", this.styles.header);
    const headerContent = this.createElement("div", this.styles.headerContent);

    // 头像
    const avatar = this.createElement("div", this.styles.avatar);
    if (this.config.logo) {
      const logoImg = this.createElement("img", this.styles.avatarImage, {
        src: this.config.logo,
        alt: "Assistant",
      });
      logoImg.onerror = () => {
        avatar.innerHTML = "";
        avatar.textContent = this.config.assistantName.charAt(0);
        Object.assign(avatar.style, this.styles.avatarText);
      };
      avatar.appendChild(logoImg);
    } else {
      avatar.textContent = this.config.assistantName.charAt(0);
      Object.assign(avatar.style, this.styles.avatarText);
    }

    // 头部文字
    const headerText = this.createElement("div", this.styles.headerText);
    const assistantName = this.createElement("h4", this.styles.assistantName, {
      textContent: this.config.assistantName,
    });
    const statusText = this.createElement("p", this.styles.statusText, {
      textContent: "Active now",
    });

    headerText.appendChild(assistantName);
    headerText.appendChild(statusText);

    headerContent.appendChild(avatar);
    headerContent.appendChild(headerText);

    // 关闭按钮（如果需要）
    if (this.config.showCloseButton && this.config.onClose) {
      const closeBtn = this.createElement(
        "button",
        this.styles.widgetCloseButton,
        {
          innerHTML: icons.close,
        }
      );
      closeBtn.addEventListener("click", this.config.onClose);
      headerContent.appendChild(closeBtn);
    }

    header.appendChild(headerContent);
    return header;
  }

  // 渲染单个消息
  private renderMessage(message: ChatMessage): HTMLElement {
    const messageContainer = this.createElement("div", {
      ...this.styles.messageContainer,
      ...(message.type === "user"
        ? this.styles.messageContainerUser
        : this.styles.messageContainerBot),
    });

    // Bot 头像
    if (message.type === "bot") {
      const botAvatar = this.createElement("div", this.styles.botAvatar);
      const avatarText = this.createElement("span", this.styles.botAvatarText, {
        textContent: this.config.assistantName.charAt(0),
      });
      botAvatar.appendChild(avatarText);
      messageContainer.appendChild(botAvatar);
    }

    // 消息气泡
    const messageBubble = this.createElement("div", {
      ...this.styles.messageBubble,
      ...(message.type === "user"
        ? this.styles.messageBubbleUser
        : this.styles.messageBubbleBot),
    });

    // 消息文本
    const messageText = this.createElement("div", this.styles.messageText);
    if (message.type === "bot") {
      // 对于 bot 消息，支持简单的 markdown 渲染
      messageText.innerHTML = this.renderMarkdown(message.message);
    } else {
      messageText.textContent = message.message;
    }

    // 时间戳
    const messageTime = this.createElement("p", this.styles.messageTime, {
      textContent: message.time,
    });

    messageBubble.appendChild(messageText);
    messageBubble.appendChild(messageTime);
    messageContainer.appendChild(messageBubble);

    return messageContainer;
  }

  // 简单的 markdown 渲染
  private renderMarkdown(text: string): string {
    return text
      .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
      .replace(/\*(.*?)\*/g, "<em>$1</em>")
      .replace(/`(.*?)`/g, "<code>$1</code>")
      .replace(/\n/g, "<br>");
  }

  // 渲染等待消息气泡
  private renderLoadingMessage(): HTMLElement {
    const messageContainer = this.createElement("div", this.styles.loadingMessageContainer);

    // Bot 头像
    const botAvatar = this.createElement("div", this.styles.botAvatar);
    const avatarText = this.createElement("span", this.styles.botAvatarText, {
      textContent: this.config.assistantName.charAt(0),
    });
    botAvatar.appendChild(avatarText);
    messageContainer.appendChild(botAvatar);

    // 等待消息气泡
    const messageBubble = this.createElement("div", this.styles.loadingMessageBubble);

    // 加载点动画
    const loadingDots = this.createElement("div", this.styles.loadingDots);

    // 创建三个点，每个有不同的延迟
    for (let i = 0; i < 3; i++) {
      const dot = this.createElement("div", {
        ...this.styles.loadingDot,
        animationDelay: `${i * 0.2}s`,
      });
      loadingDots.appendChild(dot);
    }

    const loadingText = this.createElement("span", this.styles.loadingText, {
      textContent: "Thinking",
    });

    messageBubble.appendChild(loadingText);
    messageBubble.appendChild(loadingDots);
    messageContainer.appendChild(messageBubble);

    return messageContainer;
  }

  // 渲染建议问题
  private renderSuggestedQuestions(): HTMLElement {
    const container = this.createElement(
      "div",
      this.styles.suggestedQuestionsContainer
    );

    this.config.suggestedQuestions?.forEach((question) => {
      if (question.trim()) {
        const questionBtn = this.createElement(
          "button",
          {
            ...this.styles.suggestedQuestion,
            ...(this.config.isLoading ? this.styles.suggestedQuestionDisabled : {}),
          },
          {
            textContent: question,
          }
        ) as HTMLButtonElement;

        // 设置 disabled 属性
        questionBtn.disabled = !!this.config.isLoading;

        questionBtn.addEventListener("mouseover", () => {
          if (!this.config.isLoading) {
            Object.assign(questionBtn.style, this.styles.suggestedQuestionHover);
          }
        });

        questionBtn.addEventListener("mouseout", () => {
          if (!this.config.isLoading) {
            Object.assign(questionBtn.style, this.styles.suggestedQuestion);
          }
        });

        questionBtn.addEventListener("click", () => {
          if (this.config.onSuggestedQuestion && !this.config.isLoading) {
            this.config.onSuggestedQuestion(question);
          }
        });

        container.appendChild(questionBtn);
      }
    });

    return container;
  }

  // 渲染输入区域
  private renderInputArea(): HTMLElement {
    const inputArea = this.createElement("div", this.styles.inputArea);
    const inputContainer = this.createElement(
      "div",
      this.styles.inputContainer
    );

    // 输入框
    const input = this.createElement(
      "input",
      {
        ...this.styles.input,
        ...(this.config.isLoading ? this.styles.inputDisabled : {}),
      },
      {
        type: "text",
        placeholder: "Type your message...",
        value: this.config.inputValue || "",
      }
    ) as HTMLInputElement;

    // 设置 disabled 属性
    input.disabled = !!this.config.isLoading;

    input.addEventListener("input", (e) => {
      if (this.config.onInputChange) {
        this.config.onInputChange((e.target as HTMLInputElement).value);
      }
    });

    input.addEventListener("keydown", (e) => {
      if (this.config.onKeyDown) {
        this.config.onKeyDown(e);
      }
    });

    // 发送按钮
    const sendBtn = this.createElement(
      "button",
      {
        ...this.styles.sendButton,
        ...(this.config.isLoading ? this.styles.sendButtonDisabled : {}),
      },
      {
        innerHTML: icons.send,
      }
    ) as HTMLButtonElement;

    // 设置 disabled 属性
    sendBtn.disabled = !!this.config.isLoading;

    sendBtn.addEventListener("mouseover", () => {
      if (!this.config.isLoading) {
        Object.assign(sendBtn.style, this.styles.sendButtonHover);
      }
    });

    sendBtn.addEventListener("mouseout", () => {
      Object.assign(sendBtn.style, this.styles.sendButton);
    });

    sendBtn.addEventListener("click", () => {
      if (this.config.onSendMessage && input.value.trim() && !this.config.isLoading) {
        this.config.onSendMessage(input.value.trim());
      }
    });

    inputContainer.appendChild(input);
    inputContainer.appendChild(sendBtn);

    // Powered by 链接
    const poweredBy = this.createElement("div", this.styles.poweredBy);
    poweredBy.innerHTML = `
      Powered by 
      <a href="/" style="${Object.entries(this.styles.poweredByLink)
        .map(([k, v]) => `${k.replace(/([A-Z])/g, "-$1").toLowerCase()}: ${v}`)
        .join("; ")}">
        🤖 AI Assistant
      </a>
    `;

    inputArea.appendChild(inputContainer);
    inputArea.appendChild(poweredBy);

    return inputArea;
  }

  // 渲染消息区域
  private renderMessagesArea(): HTMLElement {
    const messagesArea = this.createElement("div", this.styles.messagesArea);

    // 渲染消息，但跳过空的bot消息（在加载状态时）
    this.config.messages.forEach((message, index) => {
      // 如果是最后一条消息，且是空的bot消息，且正在加载，则跳过渲染
      const isLastMessage = index === this.config.messages.length - 1;
      const isEmptyBotMessage = message.type === 'bot' && !message.message.trim();

      if (this.config.isLoading && isLastMessage && isEmptyBotMessage) {
        // 跳过空的bot消息，稍后用等待动画替换
        return;
      }

      messagesArea.appendChild(this.renderMessage(message));
    });

    // 如果正在加载，显示等待消息（替换空的bot消息）
    if (this.config.isLoading) {
      messagesArea.appendChild(this.renderLoadingMessage());
    }

    // 渲染建议问题（仅在消息较少时显示，且不在加载状态）
    if (
      !this.config.isLoading &&
      this.config.suggestedQuestions &&
      this.config.suggestedQuestions.length > 0 &&
      this.config.messages.length <= 2
    ) {
      messagesArea.appendChild(this.renderSuggestedQuestions());
    }

    return messagesArea;
  }

  // 渲染完整的聊天界面
  render(): HTMLElement {
    const container = this.createElement("div", this.styles.container);

    // 添加自定义类名
    if (this.config.className) {
      container.className = this.config.className;
    }

    // 渲染各个部分
    container.appendChild(this.renderHeader());
    container.appendChild(this.renderMessagesArea());

    if (this.config.showInput) {
      container.appendChild(this.renderInputArea());
    }

    return container;
  }

  // 创建 Widget 切换按钮
  createToggleButton(onClick: () => void): HTMLElement {
    const button = this.createElement(
      "button",
      this.styles.widgetToggleButton,
      {
        innerHTML: icons.chat,
      }
    );

    button.addEventListener("mouseover", () => {
      Object.assign(button.style, this.styles.widgetToggleButtonHover);
    });

    button.addEventListener("mouseout", () => {
      // 保存当前的 display 属性，避免被覆盖
      const currentDisplay = button.style.display;
      Object.assign(button.style, this.styles.widgetToggleButton);
      // 如果之前设置了 display 属性，恢复它
      if (currentDisplay) {
        button.style.display = currentDisplay;
      }
    });

    button.addEventListener("click", onClick);

    return button;
  }
}
