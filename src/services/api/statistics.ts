import store from "store2";

export interface DashboardStatistics {
  overview: {
    totalConversations: number;
    totalKnowledge: number;
    totalWebsites: number;
    totalDocuments: number;
    conversations: {
      date: string;
      count: number;
    }[];
  };
  latestConversations: {
    id: string;
    name: string;
    createTime: string;
  }[];
  topLocations: {
    country: string;
    count: number;
  }[];
  topChannels: {
    channel: string;
    count: number;
  }[];
}

export interface ChatStatistics {
  totalConversations: number;
  totalChats: number;
  totalCountry: number;
  totalCity: number;
}

export const statisticsDashboard = async (
  projectId: string,
  startTime: string,
  endTime: string
): Promise<DashboardStatistics> => {
  const params = new URLSearchParams({
    projectId,
    startTime,
    endTime,
  });

  const response = await fetch(`/v1/statistics/dashboard?${params}`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

export const statisticsChat = async (
  projectId: string,
  startTime: string,
  endTime: string
): Promise<ChatStatistics> => {
  const params = new URLSearchParams({
    projectId,
    startTime,
    endTime,
  });

  const response = await fetch(`/v1/statistics/chat?${params}`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};
