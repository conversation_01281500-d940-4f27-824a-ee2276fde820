import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import ReactMarkdown from "react-markdown";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  Book,
  FileText,
  ChevronRight,
  Home,
  Search,
  ExternalLink,
  List
} from "lucide-react";
import { Input } from "@/components/ui/input";

// Import document data from virtual module
// @ts-ignore
import docsData from 'virtual:docs';

interface DocItem {
  id: string;
  title: string;
  content: string;
  filename: string;
}

interface TocItem {
  id: string;
  title: string;
  level: number;
}

// Extract table of contents from document
const extractToc = (content: string): TocItem[] => {
  const lines = content.split('\n');
  const toc: TocItem[] = [];

  lines.forEach((line, index) => {
    const match = line.match(/^(#{1,6})\s+(.+)$/);
    if (match) {
      const level = match[1].length;
      const title = match[2].trim();
      const id = `heading-${title.toLowerCase().replace(/\s+/g, '-')}`;
      toc.push({ id, title, level });
    }
  });

  return toc;
};

const Documentation = () => {
  const { docId } = useParams();
  const navigate = useNavigate();
  const [docs] = useState<DocItem[]>(docsData);
  const [currentDoc, setCurrentDoc] = useState<DocItem | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showToc, setShowToc] = useState(false);

  // Filter documents
  const filteredDocs = docs.filter(doc =>
    doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    doc.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    if (docId) {
      const doc = docs.find(d => d.id === docId);
      setCurrentDoc(doc || null);
    } else if (docs.length > 0) {
      // Show first document by default
      setCurrentDoc(docs[0]);
      navigate(`/documentation/${docs[0].id}`, { replace: true });
    }
  }, [docId, docs, navigate]);

  const handleDocSelect = (doc: DocItem) => {
    setCurrentDoc(doc);
    navigate(`/documentation/${doc.id}`);
  };

  return (
    <div>
      <div className="flex bg-gray-50">
        {/* 侧边栏 */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          {/* Header */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center gap-2 mb-4">
              <Book className="w-6 h-6 text-blue-600" />
              <h1 className="text-xl font-semibold text-gray-900">
                <a href="/dashboard">Documentation</a>
              </h1>
            </div>

            {/* Search Box */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search documentation..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Document List */}
          <ScrollArea className="flex-1">
            <div className="p-4 space-y-2">
              {filteredDocs.map((doc) => (
                <Button
                  key={doc.id}
                  variant={currentDoc?.id === doc.id ? "secondary" : "ghost"}
                  className={`w-full justify-start h-auto p-3 ${
                    currentDoc?.id === doc.id
                      ? "bg-blue-50 text-blue-700 border-blue-200"
                      : "hover:bg-gray-50"
                  }`}
                  onClick={() => handleDocSelect(doc)}
                >
                  <div className="flex items-center gap-3 w-full">
                    <FileText className="w-4 h-4 flex-shrink-0" />
                    <div className="flex-1 text-left">
                      <div className="font-medium text-sm">{doc.title}</div>
                      {/* <div className="text-xs text-gray-500 mt-1">
                        {doc.filename}
                      </div> */}
                    </div>
                    {currentDoc?.id === doc.id && (
                      <ChevronRight className="w-4 h-4" />
                    )}
                  </div>
                </Button>
              ))}

              {filteredDocs.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <FileText className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p className="text-sm">No matching documents found</p>
                </div>
              )}
            </div>
          </ScrollArea>

          {/* Footer Info */}
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>{docs.length} documents</span>
              <Badge variant="outline" className="text-xs">
                v1.0
              </Badge>
            </div>
          </div>
        </div>

        {/* 主内容区 */}
        <div className="flex-1 flex flex-col">
          {currentDoc ? (
            <>
              {/* Content Header */}
              <div className="bg-white border-b border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
                      <Home className="w-4 h-4" />
                      <span>Documentation</span>
                      <ChevronRight className="w-4 h-4" />
                      <span className="text-gray-900 font-medium">{currentDoc.title}</span>
                    </div>
                    <h1 className="text-2xl font-bold text-gray-900">{currentDoc.title}</h1>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowToc(!showToc)}
                    className="flex items-center gap-2"
                  >
                    <List className="w-4 h-4" />
                    Table of Contents
                  </Button>
                </div>
              </div>

              {/* 文档内容 */}
              <div className="flex-1 flex">
                <ScrollArea className="flex-1">
                  <div className="max-w-4xl mx-auto p-6">
                    <Card>
                      <CardContent className="p-8">
                      <div className="prose prose-gray max-w-none">
                        <ReactMarkdown
                          components={{
                            h1: ({ children, ...props }) => {
                              const text = String(children);
                              const id = `heading-${text.toLowerCase().replace(/\s+/g, '-')}`;
                              return (
                                <h1 id={id} className="text-3xl font-bold text-gray-900 mb-6 pb-3 border-b border-gray-200">
                                  {children}
                                </h1>
                              );
                            },
                            h2: ({ children, ...props }) => {
                              const text = String(children);
                              const id = `heading-${text.toLowerCase().replace(/\s+/g, '-')}`;
                              return (
                                <h2 id={id} className="text-2xl font-semibold text-gray-900 mt-8 mb-4 flex items-center gap-2">
                                  <div className="w-1 h-6 bg-blue-500 rounded"></div>
                                  {children}
                                </h2>
                              );
                            },
                            h3: ({ children, ...props }) => {
                              const text = String(children);
                              const id = `heading-${text.toLowerCase().replace(/\s+/g, '-')}`;
                              return (
                                <h3 id={id} className="text-xl font-semibold text-gray-900 mt-6 mb-3 flex items-center gap-2">
                                  <div className="w-1 h-5 bg-blue-400 rounded"></div>
                                  {children}
                                </h3>
                              );
                            },
                            h4: ({ children }) => (
                              <h4 className="text-lg font-semibold text-gray-900 mt-4 mb-2">
                                {children}
                              </h4>
                            ),
                            p: ({ children }) => (
                              <p className="text-gray-700 leading-relaxed mb-4 text-base">
                                {children}
                              </p>
                            ),
                            ul: ({ children }) => (
                              <ul className="space-y-2 mb-6 text-gray-700">
                                {children}
                              </ul>
                            ),
                            ol: ({ children }) => (
                              <ol className="space-y-2 mb-6 text-gray-700">
                                {children}
                              </ol>
                            ),
                            li: ({ children }) => (
                              <li className="leading-relaxed flex items-start gap-2">
                                <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2.5 flex-shrink-0"></span>
                                <span className="flex-1">{children}</span>
                              </li>
                            ),
                            code: ({ children, className }) => {
                              const isInline = !className;
                              if (isInline) {
                                return (
                                  <code className="bg-blue-50 text-blue-800 px-2 py-1 rounded text-sm font-mono border">
                                    {children}
                                  </code>
                                );
                              }
                              return (
                                <div className="mb-6">
                                  <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto border">
                                    <code className="text-sm font-mono">{children}</code>
                                  </pre>
                                </div>
                              );
                            },
                            blockquote: ({ children }) => (
                              <blockquote className="border-l-4 border-blue-500 pl-6 py-4 bg-blue-50 text-gray-700 mb-6 italic rounded-r-lg">
                                {children}
                              </blockquote>
                            ),
                            a: ({ href, children }) => (
                              <a
                                href={href}
                                className="text-blue-600 hover:text-blue-800 underline inline-flex items-center gap-1 font-medium"
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                {children}
                                <ExternalLink className="w-3 h-3" />
                              </a>
                            ),
                            strong: ({ children }) => (
                              <strong className="font-semibold text-gray-900">{children}</strong>
                            ),
                            em: ({ children }) => (
                              <em className="italic text-gray-800">{children}</em>
                            ),
                          }}
                        >
                          {currentDoc.content}
                        </ReactMarkdown>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </ScrollArea>

              {/* Table of Contents Sidebar */}
              {showToc && (
                <div className="w-64 border-l border-gray-200 bg-white">
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <List className="w-4 h-4" />
                      Table of Contents
                    </h3>
                    <ScrollArea className="h-[calc(100vh-12rem)]">
                      <div className="space-y-1">
                        {extractToc(currentDoc.content).map((item, index) => (
                          <button
                            key={index}
                            className={`
                              w-full text-left px-2 py-1 text-sm rounded hover:bg-gray-100 transition-colors
                              ${item.level === 1 ? 'font-semibold text-gray-900' : ''}
                              ${item.level === 2 ? 'font-medium text-gray-800 ml-2' : ''}
                              ${item.level === 3 ? 'text-gray-700 ml-4' : ''}
                              ${item.level >= 4 ? 'text-gray-600 ml-6' : ''}
                            `}
                            onClick={() => {
                              const element = document.getElementById(item.id);
                              element?.scrollIntoView({ behavior: 'smooth' });
                            }}
                          >
                            {item.title}
                          </button>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </div>
              )}
            </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <Book className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Select a Document</h2>
                <p className="text-gray-500">Choose a document from the left sidebar to view</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Documentation;
