{"formId": "f2fa21cf-211b-4d82-8c7b-213f9318e70b", "title": "User <PERSON>", "description": "Your feedback is very important to us. Please fill in the following information.", "logo": null, "settings": {"submitText": "Submit", "successMessage": "Form submitted successfully!", "errorMessage": "Submitted failed, please try again.", "allowMultipleSubmissions": true, "requireLogin": false, "collectEmail": false}, "components": [{"id": "text-input_1754488995124_gkci0b", "type": "text-input", "label": "Full Name", "placeholder": "Please enter your name", "required": true, "position": {"x": 0, "y": 0}}, {"id": "email-input_1754488995124_e5xx60", "type": "email-input", "label": "Email Address", "placeholder": "Please enter your email address", "required": true, "position": {"x": 0, "y": 1}}, {"id": "select_1754488995124_208v88", "type": "select", "label": "Feedback Type", "placeholder": "Please select feedback type", "required": true, "options": [{"label": "Product Suggestion", "value": "suggestion"}, {"label": "Bug Report", "value": "bug"}, {"label": "Feature Request", "value": "feature"}, {"label": "Other", "value": "other"}], "position": {"x": 0, "y": 2}}, {"id": "radio_1754488995124_mi0m6l", "type": "radio", "label": "Satisfaction Rating", "required": true, "options": [{"label": "Very Satisfied", "value": "5"}, {"label": "Satisfied", "value": "4"}, {"label": "Neutral", "value": "3"}, {"label": "Dissatisfied", "value": "2"}, {"label": "Very Dissatisfied", "value": "1"}], "position": {"x": 0, "y": 3}}, {"id": "textarea_1754488995124_mq0vji", "type": "textarea", "label": "Detailed Feedback", "placeholder": "Please describe your feedback in detail...", "required": true, "position": {"x": 0, "y": 4}}], "createdAt": "2025-08-06T14:03:15.228Z", "updatedAt": "2025-08-06T14:03:15.228Z"}