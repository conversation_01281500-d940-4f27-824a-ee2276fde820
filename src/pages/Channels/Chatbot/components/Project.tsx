import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

import { ChatPreview } from "@/components/ChatPreview";
import { Card } from "@/components/ui/card";
import { useAuth } from "@/context/AuthContext.tsx";
import { useProject } from "@/context/ProjectContext";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { updateProject } from "@/services/api";
import { Bot, GripVertical, Plus, Upload, X } from "lucide-react";

const Project = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const { currentProject, setCurrentProject } = useProject();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { id } = useParams();

  // 直接显示配置页面，不显示项目列表
  const [loading, setLoading] = useState(true);

  // 项目配置状态 - 使用默认值
  const [brandName, setBrandName] = useState("Hali");
  const [brandColor, setBrandColor] = useState("#FF6B35");
  const [logo, setLogo] = useState("");
  const [welcomeMessage, setWelcomeMessage] = useState(
    t("defaultWelcomeMessage")
  );
  const [suggestedQuestionsEnabled, setSuggestedQuestionsEnabled] =
    useState(true);
  const [suggestedQuestions, setSuggestedQuestions] = useState([
    t("defaultSuggestedQuestion1"),
    t("defaultSuggestedQuestion2"),
    t("defaultSuggestedQuestion3"),
  ]);

  const [draggedIndex, setDraggedIndex] = useState(null);
  const [dragOverIndex, setDragOverIndex] = useState(null);

  // 从当前项目加载配置
  useEffect(() => {
    if (currentProject) {
      // 从项目设置中加载配置，如果没有则保持默认值
      setBrandName(
        currentProject.settings?.name || currentProject.name || "Hali"
      );
      setBrandColor(currentProject.settings?.color || "#FF6B35");
      setLogo(currentProject.settings?.logo || "");
      setWelcomeMessage(
        currentProject.settings?.welcomeMsg || t("defaultWelcomeMessage")
      );
      setSuggestedQuestionsEnabled(
        currentProject.settings?.suggestedEnable ?? true
      );
      setSuggestedQuestions(
        currentProject.settings?.suggestedQuestions || [
          t("defaultSuggestedQuestion1"),
          t("defaultSuggestedQuestion2"),
          t("defaultSuggestedQuestion3"),
        ]
      );
      // setDisplayQuicksetBranding(currentProject.settings?.displayQuicksetBranding ?? true);
      setLoading(false);
    }
  }, [currentProject]);

  // 认证检查
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading, navigate]);

  const handleSave = async () => {
    try {
      // 优先使用路由参数的 id，如果没有则使用 currentProject.id
      const projectId = id || currentProject?.id;

      if (!projectId) {
        toast({
          variant: "destructive",
          title: t("error"),
          description: t("projectIdMissing"),
        });
        return;
      }


      const requestParams = {
        id: projectId,
        name: brandName,
        settings: {
          logo: logo,
          name: brandName,
          color: brandColor,
          welcomeMsg: welcomeMessage,
          suggestedEnable: suggestedQuestionsEnabled,
          suggestedQuestions: suggestedQuestions,
          // displayQuicksetBranding: displayQuicksetBranding
        },
      };

      await updateProject(requestParams);
      setCurrentProject({ ...currentProject, ...requestParams });
      toast({
        title: t("success"),
        description: t("saveConfig"),
      });
    } catch (error) {
      console.error("Failed to save project:", error);
      toast({
        variant: "destructive",
        title: t("error"),
        description: t("saveFailed"),
      });
    }
  };

  const handleLogoUpload = (event) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result;
        if (typeof result === "string") {
          setLogo(result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const removeSuggestedQuestion = (index) => {
    setSuggestedQuestions((prev) => prev.filter((_, i) => i !== index));
  };

  const addSuggestedQuestion = () => {
    setSuggestedQuestions((prev) => [...prev, ""]);
  };

  const handleDragStart = (e, index) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/plain", index.toString());

    // 找到当前拖拽的整个item容器
    const draggedElement = e.target.closest(".draggable-item");
    if (draggedElement) {
      e.dataTransfer.setDragImage(draggedElement, 0, 0);
    }
  };

  const handleDragOver = (e, index) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  const handleDrop = (e, dropIndex) => {
    e.preventDefault();
    setDragOverIndex(null);

    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null);
      return;
    }

    const newQuestions = [...suggestedQuestions];
    const draggedItem = newQuestions[draggedIndex];

    // Remove the dragged item
    newQuestions.splice(draggedIndex, 1);

    // Insert at new position
    const insertIndex = draggedIndex < dropIndex ? dropIndex - 1 : dropIndex;
    newQuestions.splice(insertIndex, 0, draggedItem);

    setSuggestedQuestions(newQuestions);
    setDraggedIndex(null);
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
    setDragOverIndex(null);
  };

  if (loading) {
    return (
      <Card>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">{t("loading")}</div>
        </div>
      </Card>
    );
  }

  // 如果没有项目数据，显示错误信息
  if (!currentProject) {
    return (
      <Card>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-lg text-gray-600 mb-4">
              {t("projectNotFound")}
            </div>
            <Button onClick={() => navigate("/projects")} variant="outline">
              {t("returnToProjectList")}
            </Button>
          </div>
        </div>
      </Card>
    );
  }

  // 项目配置视图
  return (
    <>
      <div className="min-h-screenrounded-lg">
        {/* 顶部导航栏 */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Bot className="mr-2 h-6 w-6" />
                  {t("appearance")} - {currentProject.name}
                </h1>
                <p className="text-sm text-gray-600 mt-1">
                  {t("adjustAppearance")}
                </p>
              </div>
            </div>
            <Button onClick={handleSave} className="px-6">
              {t("save")}
            </Button>
          </div>
        </div>
        {/* 主要内容区域 */}
        <div className="flex-1 p-6">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 左侧：配置表单 */}
              <div className="space-y-6">
                {/* 配置表单 */}
                <div className="space-y-6">
                  {/* Name字段 */}
                  <div>
                    <Label
                      htmlFor="brandName"
                      className="text-sm font-medium mb-2 block"
                    >
                      {t("name")}
                    </Label>
                    <Input
                      id="brandName"
                      value={brandName}
                      onChange={(e) => setBrandName(e.target.value)}
                      placeholder="Hal"
                      className="w-full"
                    />
                  </div>

                  {/* Logo */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">
                      {t("logo")}
                    </Label>
                    <input
                      type="file"
                      id="logo-upload"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                    />
                    <label htmlFor="logo-upload" className="cursor-pointer">
                      <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden hover:bg-gray-200 transition-colors border-2 border-dashed border-gray-300">
                        {logo ? (
                          <img
                            src={logo}
                            alt="Logo"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="text-center">
                            <Upload className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                            <span className="text-xs text-gray-500">
                              {t("clickToUpload")}
                            </span>
                          </div>
                        )}
                      </div>
                    </label>
                    <p className="text-xs text-gray-500 mt-2">
                      {t("svgPngJpg")}
                    </p>
                  </div>

                  {/* Brand color */}
                  <div>
                    <Label
                      htmlFor="brandColor"
                      className="text-sm font-medium mb-2 block"
                    >
                      {t("brandColor")}
                    </Label>
                    <div className="flex items-center space-x-3">
                      <Input
                        id="brandColor"
                        value={brandColor}
                        onChange={(e) => setBrandColor(e.target.value)}
                        className="w-24"
                      />
                      <input
                        type="color"
                        value={brandColor}
                        onChange={(e) => setBrandColor(e.target.value)}
                        className="w-8 h-8 rounded border cursor-pointer"
                      />
                    </div>
                  </div>

                  {/* Welcome message */}
                  <div>
                    <Label
                      htmlFor="welcomeMessage"
                      className="text-sm font-medium mb-2 block"
                    >
                      {t("welcomeMessage")}
                    </Label>
                    <Textarea
                      id="welcomeMessage"
                      value={welcomeMessage}
                      onChange={(e) => setWelcomeMessage(e.target.value)}
                      rows={4}
                      className="resize-none"
                    />
                  </div>

                  {/* Suggested questions */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <Label className="text-sm font-medium">
                        {t("suggestedQuestions")}
                      </Label>
                      <Switch
                        checked={suggestedQuestionsEnabled}
                        onCheckedChange={setSuggestedQuestionsEnabled}
                      />
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      {t("helpVisitorsStart")}
                    </p>

                    {suggestedQuestionsEnabled && (
                      <div className="space-y-2">
                        {suggestedQuestions.map((question, index) => (
                          <div
                            key={index}
                            className={`flex items-center space-x-2 p-2 rounded transition-colors ${
                              dragOverIndex === index
                                ? "bg-blue-50 border-2 border-blue-200"
                                : draggedIndex === index
                                ? "opacity-50"
                                : ""
                            }`}
                            draggable
                            onDragStart={(e) => handleDragStart(e, index)}
                            onDragOver={(e) => handleDragOver(e, index)}
                            onDragLeave={handleDragLeave}
                            onDrop={(e) => handleDrop(e, index)}
                            onDragEnd={handleDragEnd}
                          >
                            <GripVertical className="w-4 h-4 text-gray-400 cursor-grab hover:text-gray-600 active:cursor-grabbing" />
                            <Input
                              value={question}
                              onChange={(e) => {
                                const newQuestions = [...suggestedQuestions];
                                newQuestions[index] = e.target.value;
                                setSuggestedQuestions(newQuestions);
                              }}
                              className="flex-1"
                              placeholder={t("enterSuggestedQuestion")}
                            />
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeSuggestedQuestion(index)}
                              className="hover:bg-red-50 hover:text-red-600"
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}

                        {/* Add Question Button */}
                        <div className="flex justify-center mt-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={addSuggestedQuestion}
                            className="border-dashed border-2 hover:border-solid hover:bg-gray-50"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            {t("addQuestion")}
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Display Quickset branding */}
                  {/* <div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium">Display Quickset branding</Label>
                          <p className="text-sm text-gray-600 mt-1">
                            Show the Powered by Quickset link in your Web Chat.
                          </p>
                        </div>
                        <Switch
                            checked={displayQuicksetBranding}
                            onCheckedChange={setDisplayQuicksetBranding}
                        />
                      </div>
                    </div> */}
                </div>
              </div>

              {/* 右侧：聊天预览 */}
              <div className="flex justify-center">
                <div className="w-full max-w-sm">
                  <div
                    className="bg-white rounded-lg shadow-lg overflow-hidden"
                    style={{ height: "600px" }}
                  >
                    <ChatPreview
                      assistantName={brandName || "Hal"}
                      welcomeMessage={
                        welcomeMessage ||
                        `Welcome! 👋\nI'm Hal, here to assist with any questions you have. How can I help you today?`
                      }
                      color={brandColor}
                      suggestedQuestions={
                        suggestedQuestionsEnabled ? suggestedQuestions : []
                      }
                      showInput={true}
                      compact={false}
                      className="h-full"
                      logo={logo}
                      projectId={currentProject?.id}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Project;
