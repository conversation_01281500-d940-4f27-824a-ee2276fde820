# Chat Assist Builder

## 一、产品介绍

### 1. 产品背景

请帮我开发一个AI对话客服平台，通过自然语言处理技术和大语言模型能力，可以提供智能化、低成本、高效率的客服服务，客户通过平台可以自助化创建对话机器人，添加知识库，设置对话框的样式，并生成网页嵌入代码，拷贝到第三方建站平台，将对话机器人嵌入到自己的站点，帮助客户进行客服问答，提升客户体验和业务转化率。

### 2. 产品架构

```
┌──────────────────────────────────────────┐
│                客户网站                   │
│  插入JS代码后悬浮窗口展示AI客服助手         │
└──────────────┬───────────────────────────┘
               │
      交互数据（问答内容、用户信息等）
               │
┌──────────────▼──────────────────────┐
│         网站AI客服助手云端平台        │
│ ┌────────────┬────────────────────┐ │
│ │  后台管理端 │  聊天服务引擎       │ │
│ │  - 项目管理 │  - 知识库问答       │ │
│ │  - 样式配置 │  - 上下文对话管理   │ │
│ │  - 知识库管理 │  - 客户信息采集   │ │
│ │  - 用户聊天记录 │                │ │
│ └────────────┴────────────────────┘ │
└─────────────────────────────────────┘
```

---

## 二、产品用户

### 1. 用户类型

| 用户类型  | 描述                           |
| ----- | ---------------------------- |
| 已有网站客户  | 需要在自己已有网站上新增AI客服助手的企业网站拥有者或运营者 |
| 新建站客户    | 新建站的企业或个体，需要增加客服助手互动的潜在用户         |

---

## 三、业务流程

1. 企业客户注册并登录网站AI客服助手平台。
2. 在管理后台新建一个客服项目，按照如下步骤设置如下内容：

    * 添加客户网站链接地址
    * 上传知识库文档（支持Web站点url、上传本地文档、添加FAQ三种方式）
    * 设置客服聊天窗口样式，并提供实时预览功能（设置客服助理名称、窗口颜色、LOGO、欢迎词、推荐问题、窗口位置等）
    * 系统生成一段可嵌入网页的JS代码，客户将代码粘贴到其网站（例如Wix、WordPress、独立建站平台）中

---

## 四、功能需求

### 1. 客户后台功能

| 模块        | 功能描述                  |
| --------- | --------------------- |
| 用户登录/注册   | ⽀持邮箱、⼿机号注册登录，兼容OAuth（Google、Microsoft等）         |
| Dashboard数据分析 | 提供核⼼指标看板，包括访客咨询量、⾃动解决率、⽤户满意度（NPS）、对话时⻓、活跃时段/热点问题趋势 |
| 项目管理      | 新建、编辑、删除客服项目          |
| 知识库管理     | 支持Web站点url、上传本地文档、添加FAQ      |
| 样式配置      | 可视化设置客服窗口颜色、悬浮位置、图标样式 |
| 代码生成      | 生成嵌入JS代码，并提供复制按钮      |
| 聊天记录查看    | 展示用户与AI对话记录，可按项目过滤    |
| 客户信息查看    | 显示用户留下的联系方式，如邮箱、手机号等  |
| 统计与报表 | 展示客服接待量、转化率、活跃时间等数据指标 |

### 2. 网站端客服窗口

| 功能点   | 描述                   |
| ----- | -------------------- |
| 悬浮窗口  | 支持在网页右下角展示，点击展开对话框   |
| 欢迎语配置 | 初次打开自动发送欢迎语          |
| 与AI对话 | 支持自由文本提问，支持多轮对话上下文记忆 |
| 留资引导  | AI识别意图后主动引导留下联系方式    |
| 移动端适配 | 支持响应式布局，适应移动端屏幕展示    |

---

## 五、非功能需求
1. 网站需要支持多语种切换，支持中文、英文，默认为英文；
2. 要求配色简洁清新、布局合理；
---

## technologies

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
