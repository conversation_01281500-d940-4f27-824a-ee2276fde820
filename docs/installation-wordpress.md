# WordPress Installation

Add the chat assistant to your WordPress website using our plugin or manual installation.

## Overview

WordPress installation options:
- **Plugin Method** (Recommended) - Easy setup with admin interface
- **Manual Method** - Direct code integration for advanced users
- **Widget Method** - Add chat to specific widget areas
- **Shortcode Method** - Embed chat in posts and pages

---

## Method 1: Plugin Installation (Recommended)

### Step 1: Install the Plugin

**From WordPress Admin:**
1. Log in to your WordPress admin dashboard (`yoursite.com/wp-admin`)
2. Navigate to **Plugins** → **Add New**
3. Search for "Chat Assistant" or your plugin name
4. Click **Install Now**
5. Click **Activate** after installation

**Manual Plugin Upload:**
1. Download the plugin ZIP file from your account
2. Go to **Plugins** → **Add New** → **Upload Plugin**
3. Choose the ZIP file and click **Install Now**
4. Activate the plugin

### Step 2: Get Your Chat ID

1. Log in to your chat assistant account
2. Navigate to **Test & Install**
3. Click the **WordPress** button
4. Copy your Web Chat ID from the popup

### Step 3: Configure the Plugin

1. In WordPress admin, find **Chat Assistant** in the sidebar
2. Paste your Chat ID into the "Web Chat ID" field
3. Configure additional settings:
   - **Enable/Disable**: Toggle chat on/off
   - **Pages**: Choose where to show the chat
   - **User Roles**: Select which users can see the chat
   - **Position**: Choose chat widget position
4. Click **Save Changes**

### Step 4: Verify Installation

1. Visit your website frontend
2. Check that the chat widget appears
3. Test the chat functionality
4. Verify it appears on the correct pages

---

## Method 2: Manual Installation

### Option A: Theme Footer

1. **Access Theme Editor**
   - Go to **Appearance** → **Theme Editor**
   - Select your active theme
   - Open `footer.php`

2. **Add the Code**
   - Find the closing `</body>` tag
   - Add your chat script before it:

```php
<!-- Chat Assistant -->
<script async
  src="https://your-chat-script-url"
  project-id="project-id">
</script>
</body>
</html>
```

3. **Save Changes**
   - Click **Update File**
   - Clear any caching plugins

### Option B: Functions.php Method

Add this to your theme's `functions.php`:

```php
function add_chat_assistant() {
    ?>
    <script async
      src="https://your-chat-script-url"
      project-id="project-id">
    </script>
    <?php
}
add_action('wp_footer', 'add_chat_assistant');
```

### Option C: Child Theme (Recommended)

Create a child theme to preserve customizations:

1. **Create Child Theme Folder**
   - `/wp-content/themes/your-theme-child/`

2. **Create style.css**
```css
/*
Theme Name: Your Theme Child
Template: your-parent-theme
*/

@import url("../your-parent-theme/style.css");
```

3. **Create functions.php**
```php
<?php
function add_chat_assistant() {
    ?>
    <script async
      src="https://your-chat-script-url"
      project-id="project-id">
    </script>
    <?php
}
add_action('wp_footer', 'add_chat_assistant');
?>
```

---

## Method 3: Widget Integration

### Custom HTML Widget

1. Go to **Appearance** → **Widgets**
2. Add a **Custom HTML** widget to your desired area
3. Add the chat script:

```html
<div id="chat-widget-area">
    <script async
      src="https://your-chat-script-url"
      project-id="project-id">
    </script>
</div>
```

### Sidebar Chat Widget

For embedded chat in sidebar:

```html
<div class="sidebar-chat">
    <h3>Need Help?</h3>
    <div id="sidebar-chat-container" style="height: 400px;"></div>
    <script async
      src="https://your-chat-script-url"
      project-id="project-id"
      data-target="sidebar-chat-container"
      >
    </script>
</div>
```

---

## Method 4: Shortcode Integration

### Create Custom Shortcode

Add to your theme's `functions.php`:

```php
function chat_assistant_shortcode($atts) {
    $atts = shortcode_atts(array(
        'id' => 'your-default-chat-id',
        'type' => 'widget', // 'widget' or 'embed'
        'height' => '400px',
        'width' => '100%'
    ), $atts);
    
    if ($atts['type'] === 'embed') {
        $container_id = 'chat-' . uniqid();
        return '
        <div id="' . $container_id . '" style="width: ' . $atts['width'] . '; height: ' . $atts['height'] . ';"></div>
        <script async
          src="https://your-chat-script-url"
          project-id="project-id"
          data-target="' . $container_id . '"
        </script>';
    } else {
        return '
        <script async
          src="https://your-chat-script-url"
          project-id="project-id">
        </script>';
    }
}
add_shortcode('chat_assistant', 'chat_assistant_shortcode');
```

### Using the Shortcode

**In Posts/Pages:**
```
[chat_assistant]
[chat_assistant id="custom-chat-id"]
[chat_assistant type="embed" height="500px"]
```

**In PHP Templates:**
```php
echo do_shortcode('[chat_assistant]');
```

---

## Advanced Configuration

### Conditional Loading

Show chat only on specific pages:

```php
function add_conditional_chat() {
    // Only on contact page
    if (is_page('contact')) {
        ?>
        <script async
          src="https://your-chat-script-url"
          project-id="project-id">
        </script>
        <?php
    }
    
    // Only on product pages (WooCommerce)
    if (is_product()) {
        ?>
        <script async
          src="https://your-chat-script-url"
          project-id="project-id">
        </script>
        <?php
    }
}
add_action('wp_footer', 'add_conditional_chat');
```

### User Role Based Loading

```php
function add_role_based_chat() {
    // Only for logged-in users
    if (is_user_logged_in()) {
        ?>
        <script async
          src="https://your-chat-script-url"
          project-id="project-id">
        </script>
        <?php
    }
    
    // Only for specific roles
    if (current_user_can('subscriber')) {
        ?>
        <script async
          src="https://your-chat-script-url"
          project-id="project-id">
        </script>
        <?php
    }
}
add_action('wp_footer', 'add_role_based_chat');
```

### Custom Styling

Add custom CSS for WordPress themes:

```css
/* Add to your theme's style.css or Customizer */

/* Adjust chat position for WordPress admin bar */
.admin-bar #ai-chat-widget {
    bottom: 52px !important; /* Account for admin bar height */
}

/* Mobile admin bar adjustment */
@media screen and (max-width: 782px) {
    .admin-bar #ai-chat-widget {
        bottom: 66px !important;
    }
}

/* Hide chat on specific pages */
.page-id-123 #ai-chat-widget {
    display: none !important;
}

/* Custom styling for embedded chat */
.sidebar-chat {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.sidebar-chat h3 {
    margin-top: 0;
    color: #333;
    font-size: 18px;
}
```

---

## WooCommerce Integration

### Product Page Chat

```php
function add_product_chat() {
    if (is_product()) {
        global $product;
        $product_id = $product->get_id();
        ?>
        <div class="product-chat">
            <h3>Questions about this product?</h3>
            <div id="product-chat-<?php echo $product_id; ?>" style="height: 300px;"></div>
            <script async
              src="https://your-chat-script-url"
              project-id="project-id"
              data-target="product-chat-<?php echo $product_id; ?>"
              >
            </script>
        </div>
        <?php
    }
}
add_action('woocommerce_single_product_summary', 'add_product_chat', 25);
```

### Cart Page Chat

```php
function add_cart_chat() {
    if (is_cart()) {
        ?>
        <div class="cart-help">
            <h3>Need help with your order?</h3>
            <script async
              src="https://your-chat-script-url"
              project-id="project-id">
            </script>
        </div>
        <?php
    }
}
add_action('woocommerce_after_cart_table', 'add_cart_chat');
```

---

## Troubleshooting

### Common WordPress Issues

**Plugin Conflicts**
- Deactivate other plugins temporarily
- Check for JavaScript errors in console
- Test with default theme

**Caching Issues**
- Clear all caching plugins
- Purge CDN cache
- Hard refresh browser (Ctrl+F5)

**Theme Compatibility**
- Test with default WordPress theme
- Check if theme overrides footer
- Verify theme follows WordPress standards

**Admin Bar Overlap**
- Add CSS to adjust chat position
- Account for mobile admin bar height
- Test with admin bar hidden

### Performance Optimization

**Lazy Loading**
```php
function lazy_load_chat() {
    ?>
    <script>
    // Load chat when user scrolls
    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            var script = document.createElement('script');
            script.async = true;
            script.src = 'https://your-chat-script-url';
            script.setAttribute('project-id', 'your-chat-id');
            document.body.appendChild(script);
            window.removeEventListener('scroll', arguments.callee);
        }
    });
    </script>
    <?php
}
add_action('wp_footer', 'lazy_load_chat');
```

**Conditional Loading for Mobile**
```php
function mobile_conditional_chat() {
    if (wp_is_mobile()) {
        // Mobile-specific chat configuration
        ?>
        <script async
          src="https://your-chat-script-url"
          project-id="project-id">
        </script>
        <?php
    } else {
        // Desktop chat configuration
        ?>
        <script async
          src="https://your-chat-script-url"
          project-id="project-id">
        </script>
        <?php
    }
}
add_action('wp_footer', 'mobile_conditional_chat');
```

---

## Security Considerations

### Sanitize User Input

When creating admin interfaces:

```php
function save_chat_settings() {
    if (isset($_POST['chat_id'])) {
        $chat_id = sanitize_text_field($_POST['chat_id']);
        update_option('chat_assistant_id', $chat_id);
    }
}
```

### Capability Checks

```php
function chat_admin_page() {
    if (!current_user_can('manage_options')) {
        wp_die(__('You do not have sufficient permissions.'));
    }
    // Admin page content
}
```

---