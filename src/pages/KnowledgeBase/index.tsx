import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/context/AuthContext.tsx";
import { useProject } from "@/context/ProjectContext";
import { useTranslation } from "@/hooks/useTranslation";
import {
  BookOpen,
  FileText,
  Globe,
  MessageCircleQuestion,
} from "lucide-react";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { FaqTab } from "./FaqTab";
import { WebsiteTab } from "./WebsiteTab";
import { DocumentTab } from "./DocumentTab";
import { StatisticsCards } from "./StatisticsCards";
import { useKnowledgeBase } from "./useKnowledgeBase";

const KnowledgeBase = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const { currentProject } = useProject();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // 使用自定义 hook 管理知识库状态
  const {
    activeTab,
    statistics,
    handleTabChange,
    fetchStatistics,
  } = useKnowledgeBase(currentProject?.id);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading, navigate]);

  // 如果项目ID不存在，不渲染内容
  if (!currentProject?.id) {
    return <DashboardLayout>Loading...</DashboardLayout>;
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <BookOpen className="mr-2 h-6 w-6" />
            {t("knowledgeBase")}
          </h1>
          <p className="text-gray-600 mt-1">{t("manageKnowledgeBase")}</p>
        </div>

        {/* 统计卡片 */}
        <StatisticsCards
          statistics={statistics}
          totalItems={statistics.reduce((sum, stat) => sum + stat.count, 0)}
        />

        {/* Tabs组件 */}
        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="space-y-6"
        >
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="FAQ" className="flex items-center gap-2">
              <MessageCircleQuestion className="w-4 h-4" />
              FAQ
            </TabsTrigger>
            <TabsTrigger value="Website" className="flex items-center gap-2">
              <Globe className="w-4 h-4" />
              Website
            </TabsTrigger>
            <TabsTrigger value="Document" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Document
            </TabsTrigger>
          </TabsList>

          {/* FAQ标签页 */}
          <TabsContent value="FAQ">
            <FaqTab
              projectId={currentProject.id}
              onStatisticsUpdate={fetchStatistics}
            />
          </TabsContent>

          {/* Website标签页 */}
          <TabsContent value="Website">
            <WebsiteTab
              projectId={currentProject.id}
              onStatisticsUpdate={fetchStatistics}
            />
          </TabsContent>

          {/* Document标签页 */}
          <TabsContent value="Document">
            <DocumentTab
              projectId={currentProject.id}
              onStatisticsUpdate={fetchStatistics}
            />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default KnowledgeBase;