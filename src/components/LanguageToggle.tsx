
import { Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/hooks/useTranslation';

export const LanguageToggle = () => {
  const { language, changeLanguage, t } = useTranslation();

  const toggleLanguage = () => {
    changeLanguage(language === 'en' ? 'zh' : 'en');
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={toggleLanguage}
      className="flex items-center gap-2"
    >
      <Globe className="w-4 h-4" />
      {language === 'en' ? t('chinese') : t('english')}
    </Button>
  );
};
