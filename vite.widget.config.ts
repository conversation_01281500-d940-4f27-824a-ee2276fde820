import { defineConfig } from "vite";
import path from "path";

// Widget专用的Vite配置
export default defineConfig({
  build: {
    // 输出目录
    outDir: 'dist',
    // 清空输出目录
    emptyOutDir: false,
    // 库模式配置
    lib: {
      entry: path.resolve(__dirname, 'src/widget/index.ts'),
      name: 'ChatWidget',
      fileName: 'widget',
      formats: ['iife'] // 立即执行函数表达式，适合直接在浏览器中使用
    },
    rollupOptions: {
      // 确保外部化处理那些你不想打包进库的依赖
      external: [],
      output: {
        // 在 IIFE 模式下为全局变量提供一个名称
        globals: {},
        // 自定义输出文件名
        entryFileNames: 'widget.js',
        // 禁用代码分割，确保所有代码都在一个文件中
        inlineDynamicImports: true,
      }
    },
    // 生成源码映射
    sourcemap: false,
    // 最小化输出 - 启用压缩
    minify: 'terser',
    // Terser 压缩选项
    terserOptions: {
      compress: {
        drop_console: true, // 移除 console.log
        drop_debugger: true, // 移除 debugger
        pure_funcs: ['console.log', 'console.info', 'console.debug'], // 移除指定的纯函数调用
      },
      mangle: {
        toplevel: true, // 混淆顶级作用域的变量名
      },
      format: {
        comments: false, // 移除注释
      },
    },
    // 目标环境
    target: 'es2015'
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  define: {
    // 在构建时替换环境变量
    'import.meta.env.DEV': false,
    'import.meta.env.PROD': true,
    // 为浏览器环境定义 process.env.NODE_ENV
    'process.env.NODE_ENV': '"production"',
  }
});
