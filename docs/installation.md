# Installation Overview

Choose your platform to get started with installing the chat assistant.

## Quick Start

Before installing on any platform, you need to:

1. Log in to your account
2. Navigate to **Test & Install** in the sidebar
3. Copy the installation code provided
4. Follow the platform-specific guide below

## Installation Guides by Platform

### Website Integration
- **[Standard Website](./installation-website.md)** - Add to any HTML website
- **[Framer](./installation-framer.md)** - Custom code and embed integration for Framer sites

### Content Management Systems
- **[WordPress](./installation-wordpress.md)** - Plugin and manual installation methods
- **[Wix](./installation-wix.md)** - Add to your Wix website
- **[Squarespace](./installation-squarespace.md)** - Code injection method

### E-commerce Platforms
- **[Shopify](./installation-shopify.md)** - Add to your Shopify store

### Development Tools
- **[Google Tag Manager](./installation-gtm.md)** - Manage through GTM
- **[WebView (Mobile Apps)](./installation-webview.md)** - Android and iOS app integration

## General Testing

After installation on any platform:

1. **Verify Appearance**: Check that the chat widget appears on your site
2. **Test Functionality**: Send a test message to ensure it responds
3. **Check Mobile**: Verify it works on mobile devices
4. **Review Analytics**: Monitor the Test & Install page for connection status

## Common Troubleshooting

### Chat Not Appearing
- Ensure the code is placed before `</body>`
- Check for JavaScript errors in browser console
- Verify your Project ID is correct
- Make sure the chat is enabled in your dashboard

### Chat Not Responding
- Check your knowledge base is properly configured
- Verify your project settings are complete
- Ensure you have sufficient credits/quota

## Need Help?

If you encounter issues with any platform:
1. Check the platform-specific troubleshooting section
2. Review the specific installation guide for your platform
3. Contact support with your platform details

---
