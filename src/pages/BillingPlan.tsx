import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
// import { Progress } from "@/components/ui/progress"; // 暂时不需要
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/context/AuthContext";
import {
  getAvailablePlans,
  getCurrentPlan,
  getCurrentProduct,
  type AvailablePlan,
  type CurrentPlan,
  type CurrentProductResponse,
} from "@/services/api";
import { ExternalLink } from "lucide-react";
import { useEffect, useState } from "react";

// 根据计划类型生成功能列表
const getPlanFeatures = (plan: AvailablePlan): (JSX.Element | string)[] => {
  const baseFeatures = [
    <span>
      <b>{plan.responses}</b> support responses/mo
    </span>,
    <span>
      <b>{plan.bots}</b> AI chatbots
    </span>,
    <span>
      <b>{plan.documents}</b> sources for training per chatbot
    </span>,
    <span>
      <b>{plan.teamMembers}</b> seats per chatbot
    </span>,
  ];

  return [...baseFeatures, ...(JSON.parse(plan.additionalInfo) ?? [])];
};

// 使用进度组件
const UsageProgress = ({
  used,
  total,
  label,
}: {
  used: number;
  total: number;
  label: string;
}) => {
  const percentage = Math.min(Math.round((used / total) * 100), 100);

  return (
    <div className="space-y-2">
      <div className="flex justify-between text-sm">
        <span className="font-medium">{label}</span>
        <span>
          {used} <span className="text-gray-500">/ {total}</span>
        </span>
      </div>
      <Progress value={percentage} className="h-2" />
    </div>
  );
};

const BillingPlan = () => {
  const { user } = useAuth();
  const [intervalType, setBillingCycle] = useState<"Monthly" | "Yearly">(
    "Yearly"
  );
  const [currentPlan, setCurrentPlan] = useState<CurrentPlan | null>(null);
  const [currentProduct, setCurrentProduct] = useState<CurrentProductResponse | null>(null);
  const [allPlans, setAllPlans] = useState<AvailablePlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取计划数据
  useEffect(() => {
    const fetchPlanData = async () => {
      try {
        setLoading(true);
        const [current, product, available] = await Promise.all([
          getCurrentPlan(),
          getCurrentProduct(),
          getAvailablePlans(),
        ]);
        setCurrentPlan(current);
        setCurrentProduct(product);
        setAllPlans(available);
      } catch (err) {
        console.error("Failed to fetch plan data:", err);
        setError("Failed to fetch plan information");
        // 如果API失败，设置为null
        setCurrentPlan(null);
        setCurrentProduct(null);
      } finally {
        setLoading(false);
      }
    };

    fetchPlanData();
  }, []);

  // 实现计算整数相除保留两位小数
  const formatPrice = (price: number, divide: number = 100) => {
    return (price / divide).toFixed(2);
  };

  const plans = allPlans.filter((p) => p.interval == intervalType);
  return (
    <DashboardLayout>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Billing & Plan</h1>
        <p className="text-gray-600 mt-2">
          Manage plan and billing settings for your Quickset account
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <Tabs defaultValue="plans" className="space-y-6">
        <TabsList className="w-full max-w-md">
          <TabsTrigger value="plans" className="flex-1">
            Plans
          </TabsTrigger>
          <TabsTrigger value="billing" className="flex-1">
            Billing
          </TabsTrigger>
        </TabsList>

        <TabsContent value="plans" className="space-y-6">
          {/* 计划选择 */}
          <div className="flex justify-end mb-4">
            <div className="bg-gray-100 rounded-full p-1 inline-flex">
              <button
                className={`px-4 py-1.5 text-sm rounded-full ${
                  intervalType === "Yearly" ? "bg-white shadow-sm" : ""
                }`}
                onClick={() => {
                  setBillingCycle("Yearly");
                }}
              >
                Yearly
              </button>
              <button
                className={`px-4 py-1.5 text-sm rounded-full ${
                  intervalType === "Monthly" ? "bg-white shadow-sm" : ""
                }`}
                onClick={() => {
                  setBillingCycle("Monthly");
                }}
              >
                Monthly
              </button>
            </div>
          </div>

          {loading ? (
            <div className="py-8 flex justify-center">
              <div className="animate-pulse flex space-x-8">
                <div className="flex-1 space-y-4">
                  <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-40 bg-gray-200 rounded"></div>
                </div>
                <div className="flex-1 space-y-4">
                  <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-40 bg-gray-200 rounded"></div>
                </div>
                <div className="flex-1 space-y-4">
                  <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-40 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          ) : (
            <>
              {plans.length > 0 ? (
                (() => {
                  // 渲染计划卡片的函数
                  const renderPlanCard = (plan: AvailablePlan) => {
                    let saveMonthly = 0;
                    if (plan.interval === "Yearly") {
                      const monthlyPrice =
                        allPlans.find(
                          (p) => p.type == plan.type && p.interval === "Monthly"
                        )?.price || 0;
                      saveMonthly = monthlyPrice * 12 - plan.price;
                    }
                    return (
                      <Card key={plan.id} className={`relative `}>
                        {plan.popular === 1 && (
                          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2"></div>
                        )}
                        <CardHeader>
                          <CardTitle>
                            <div className="flex items-end">
                              {plan.name}
                              {plan.popular === 1 && (
                                <Badge className="bg-blue-500 text-white border-0 ml-2">
                                  Most Popular
                                </Badge>
                              )}
                            </div>
                          </CardTitle>
                          <CardDescription>{plan.description}</CardDescription>
                          <div className="mt-4">
                            <div className="flex items-baseline">
                              <span className="text-3xl font-bold">
                                ${formatPrice(plan.price)}
                              </span>
                              <span className="text-gray-500 ml-1">/mo</span>
                            </div>
                            {intervalType === "Yearly" && (
                              <>
                                <p className="text-sm text-green-600 mt-1">
                                  Save ${formatPrice(saveMonthly)}
                                </p>
                                <p className="text-sm text-gray-500 mt-1">
                                  Billed at ${formatPrice(plan.price, 100 * 12)}
                                  /month
                                </p>
                              </>
                            )}
                          </div>
                        </CardHeader>
                        <CardContent>
                          <Button
                            className="w-full mb-6"
                            variant={
                              plan.type === "Free" ? "outline" : "default"
                            }
                            disabled={plan.type === "Free"}
                            onClick={() =>
                              window.open(
                                `${plan.paymentLink}?prefilled_email=${user.email}&client_reference_id=${plan.id}`
                              )
                            }
                          >
                            {plan.type === "Free" ? "Current Plan" : "Upgrade"}
                          </Button>
                          <ul className="space-y-3">
                            {getPlanFeatures(plan).map((feature, index) => (
                              <li
                                key={index}
                                className="flex items-start gap-2 text-sm"
                              >
                                <svg
                                  className="h-5 w-5 flex-shrink-0 text-green-500"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M5 13l4 4L19 7"
                                  />
                                </svg>
                                <span>{feature}</span>
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>
                    );
                  };

                  return (
                    <div className="space-y-6">
                      {/* 第一行：3列布局（宽屏）或2列布局（窄屏） */}
                      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
                        {plans.map(renderPlanCard)}
                      </div>
                    </div>
                  );
                })()
              ) : (
                // 如果没有API数据，显示加载状态
                <div className="py-8 flex justify-center">
                  <div className="text-gray-500">Loading plans...</div>
                </div>
              )}
            </>
          )}
        </TabsContent>

        <TabsContent value="billing" className="space-y-6">
          {/* 当前计划信息 */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <CardTitle>Current Plan</CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1.5"
                  asChild
                >
                  <a href="#" target="_blank" rel="noopener noreferrer">
                    Unlock Pro Features <ExternalLink className="h-3.5 w-3.5" />
                  </a>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="py-8 flex justify-center">
                  <div className="animate-pulse flex flex-col items-center">
                    <div className="h-6 w-24 bg-gray-200 rounded mb-4"></div>
                    <div className="h-4 w-48 bg-gray-200 rounded"></div>
                  </div>
                </div>
              ) : currentProduct ? (
                <>
                  <h3 className="text-2xl font-bold mb-1">
                    {currentProduct.productName}
                  </h3>
                  <p className="text-sm text-gray-500 mb-6">
                    Expires on: {new Date(currentProduct.expireTime).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>

                  <div className="space-y-6">
                    <div>
                    <h4 className="text-sm font-medium mb-4">
                        Plan limits usage
                      </h4>
                      <div className="space-y-4">
                        <UsageProgress
                          used={currentPlan.limits?.aiChatbots?.used || 0}
                          total={
                            currentPlan.limits?.aiChatbots?.total ||
                            currentPlan.bots ||
                            1
                          }
                          label="AI Chatbots"
                        />
                        <UsageProgress
                          used={currentPlan.limits?.monthlyResponses?.used || 0}
                          total={
                            currentPlan.limits?.monthlyResponses?.total ||
                            currentPlan.responses ||
                            1000
                          }
                          label="Monthly response credits"
                        />
                        <div className="flex justify-between items-center pt-2">
                          <span className="text-sm font-medium">
                            Additional credits
                          </span>
                          <div className="flex items-center gap-2">
                            <span>
                              {currentPlan.limits?.additionalCredits || 0}
                            </span>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-7 px-2 text-xs"
                            >
                              Purchase more
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-4">
                        Bot limits usage
                      </h4>
                      <div className="space-y-4">
                        <UsageProgress
                          used={
                            currentPlan.botLimits?.trainingResources?.used || 26
                          }
                          total={
                            currentPlan.botLimits?.trainingResources?.total ||
                            2000
                          }
                          label="Sources for training"
                        />
                        <UsageProgress
                          used={
                            currentPlan.botLimits?.seatsPerChatbot?.used || 0
                          }
                          total={
                            currentPlan.botLimits?.seatsPerChatbot?.total ||
                            currentPlan.teamMembers ||
                            3
                          }
                          label="Seats per chatbot"
                        />
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="py-4 text-center text-gray-500">
                  Unable to load product information
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </DashboardLayout>
  );
};

export default BillingPlan;
