import {useState, useEffect, useRef} from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { OnboardingProgress } from '@/components/OnboardingProgress';
import { useOnboardingData } from '@/hooks/useOnboardingData';
import {useAuth} from "@/context/AuthContext.tsx";
import { useToast } from '@/hooks/use-toast';
import { toast } from '@/hooks/use-toast';

const OnboardingStarter = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const { data, updateData } = useOnboardingData();
  const [websiteUrl, setWebsiteUrl] = useState(data.websiteUrl);
  const { toast } = useToast();
  const hasInitialized = useRef(false);

  // 检查URL是否有效（非空且已修剪空白字符）
  const isUrlValid = websiteUrl?.trim().length > 0;

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, isLoading, navigate]);

  // 分离初始化逻辑
  useEffect(() => {
    if (!hasInitialized.current && data.websiteUrl) {
      setWebsiteUrl(data.websiteUrl);
      hasInitialized.current = true;
    }
  }, [data.websiteUrl]);

  const handleNext = () => {
    if (!websiteUrl.trim()) {
      toast({
        title: "Website URL is required",
        description: "Please enter your website address to continue.",
        variant: "destructive",
      });
      return;
    }

    updateData({ websiteUrl });

    // 可选：添加小延迟确保数据已保存
    setTimeout(() => {
      navigate('/onboarding-form');
    }, 100);
  };

  const handleSkip = () => {
    navigate('/dashboard');
  };

  return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col">
        <OnboardingProgress currentStep={1} />

        {/* Main content area */}
        <div className="flex-1 flex flex-col relative">
          {/* Content positioned higher up */}
          <div className="flex-1 overflow-y-auto p-4 pb-20">
            <div className="w-full max-w-4xl mx-auto">
              <div className="text-center">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                  What's your website address?
                </h1>
                <p className="text-lg text-gray-600 mb-12 max-w-2xl mx-auto">
                  Provide your website URL to help train your AI agent. We'll start by pulling in
                  key data to get your chatbot ready in minutes.
                </p>

                <div className="max-w-md mx-auto mb-8">
                  <Input
                      type="url"
                      placeholder="Enter your website address"
                      value={websiteUrl}
                      onChange={(e) => setWebsiteUrl(e.target.value)}
                      className="h-12 text-base"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Fixed bottom right button */}
          <div className="absolute bottom-8 right-8">
            <Button
                onClick={handleNext}
                disabled={!isUrlValid}
                className={`px-8 py-2 h-10 transition-all duration-200 ${
                    !isUrlValid
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed hover:bg-gray-300'
                        : 'bg-primary text-primary-foreground hover:bg-primary/90'
                }`}
            >
              Next
            </Button>
          </div>
        </div>
      </div>
  );
};

export default OnboardingStarter;