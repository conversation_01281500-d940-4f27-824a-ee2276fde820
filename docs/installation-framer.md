# Framer Installation

Integrate the AI-powered Web Chat on your Framer site to deliver real-time support and enhance visitor interaction.

## Overview

Framer installation methods:
- **Custom Code Integration** - Add to head tag for site-wide installation
- **Embed Component** - Use Framer's embed component for specific pages
- **Widget Style Options** - Choose between bubble and embed formats

---

## Copy Your Installation Code

In your chatbot sidebar menu, go to the **Test & Install** page and copy the code snippet provided.

---

## Method 1: Custom Code Integration (Recommended)

### Step 1: Log in to Your Framer Account

Select the website where you want to add the chatbot.

### Step 2: Open the Settings Page

Click the gear ⚙️ icon in the top-right corner and choose **General** from the sidebar.

### Step 3: Insert the Code

1. Scroll to **Custom Code** section
2. Paste your installation code at the end of the head tag:

```html
<script async
  src="https://your-chat-script-url"
  project-id="project-id">
</script>
```

3. Click **Save** to apply changes

### Step 4: Publish Your Site

Click **Publish** to make the chatbot live on your site.

---

## Method 2: Embed Component Integration

### Step 1: Configure Embed Style

1. **Go to Appearance in the sidebar**
   - Under the **Styling** tab
   - Switch **Widget style** from **Bubble** to **Embed**
   - Save the changes

### Step 2: Add Embed Component in Framer

1. **Select your page in Framer**
   - Click **Insert** in the toolbar

2. **Choose Embed Component**
   - Select the **Embed** component from the Utility section
   - Place it on your page where you want the chat to appear

3. **Configure the Embed**
   - In the sidebar, select **HTML type**
   - Paste the embed code:

```html
<div id="framer-chat-container" style="width: 100%; height: 400px;">
  <script async
    src="https://your-chat-script-url"
    project-id="project-id"
  </script>
</div>
```

### Step 3: Publish Your Site

Click **Publish** to activate the embedded chatbot.

---

## Styling for Framer

### Custom CSS Integration

Add custom styling through Framer's Custom Code section:

```css
/* Framer-specific chat styling */
#ai-chat-widget {
    z-index: 9999 !important;
    font-family: var(--framer-font-family, -apple-system, BlinkMacSystemFont, sans-serif);
}

/* Adjust for Framer's responsive breakpoints */
@media (max-width: 810px) {
    #ai-chat-widget {
        bottom: 20px !important;
        right: 20px !important;
        width: 60px !important;
        height: 60px !important;
    }
}

/* Match Framer theme colors */
#ai-chat-widget-button {
    background: var(--token-primary-color, #0099ff) !important;
}

.chat-window {
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
}

/* Embedded chat styling */
.framer-embed-chat {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 8px;
    overflow: hidden;
}
```

### Responsive Design

```css
/* Framer responsive adjustments */
#ai-chat-widget {
    transition: all 0.3s ease;
}

/* Desktop */
@media (min-width: 1200px) {
    #ai-chat-widget {
        bottom: 30px;
        right: 30px;
    }
}

/* Tablet */
@media (max-width: 810px) and (min-width: 480px) {
    #ai-chat-widget {
        bottom: 25px;
        right: 25px;
    }
}

/* Mobile */
@media (max-width: 480px) {
    #ai-chat-widget {
        bottom: 15px;
        right: 15px;
        width: 50px;
        height: 50px;
    }
}
```

---

## Advanced Framer Integration

### Component-Based Integration

For more control, create a custom Framer component:

1. **Create New Component**
   - In Framer, create a new component
   - Name it "ChatWidget"

2. **Add Code Override**

```javascript
import { Override } from "framer"

export const ChatWidget: Override = () => {
    return {
        onMount() {
            // Load chat script when component mounts
            const script = document.createElement('script');
            script.async = true;
            script.src = 'https://your-chat-script-url';
            script.setAttribute('project-id', 'project-id');
            document.body.appendChild(script);
        }
    }
}
```

3. **Apply Override**
   - Apply the override to any element on your page
   - The chat will load when that element is mounted

### Page-Specific Chat

Load different chat configurations for different pages:

```javascript
export const PageSpecificChat: Override = () => {
    return {
        onMount() {
            const currentPath = window.location.pathname;
            let projectId = 'default-project-id';
            
            // Different chat for different pages
            if (currentPath.includes('/support')) {
                projectId = 'support-project-id';
            } else if (currentPath.includes('/sales')) {
                projectId = 'sales-project-id';
            }
            
            const script = document.createElement('script');
            script.async = true;
            script.src = 'https://your-chat-script-url';
            script.setAttribute('project-id', projectId);
            document.body.appendChild(script);
        }
    }
}
```

---

## Troubleshooting

### Common Framer Issues

**Chat Not Appearing**
- Check that the code is added to the correct Custom Code section
- Verify the project ID is correct
- Ensure the site is published (not just preview mode)

**Embed Component Issues**
- Make sure HTML type is selected in embed settings
- Check that the embed code is complete
- Verify the container has proper dimensions

**Mobile Display Issues**
- Test on actual mobile devices
- Check Framer's responsive breakpoints
- Verify touch interactions work properly

### Performance Optimization

**Lazy Loading**
```javascript
// Load chat only when user scrolls
let chatLoaded = false;

window.addEventListener('scroll', function() {
    if (!chatLoaded && window.scrollY > 300) {
        chatLoaded = true;
        
        const script = document.createElement('script');
        script.async = true;
        script.src = 'https://your-chat-script-url';
        script.setAttribute('project-id', 'project-id');
        document.body.appendChild(script);
    }
});
```

**Conditional Loading**
```javascript
// Load chat only on specific pages
if (window.location.pathname === '/contact') {
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-chat-script-url';
    script.setAttribute('project-id', 'project-id');
    document.body.appendChild(script);
}
```

---