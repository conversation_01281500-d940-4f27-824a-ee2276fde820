# Shopify Installation

Add the chat assistant to your Shopify store to provide instant customer support and boost sales.

## Overview

Benefits for Shopify stores:
- **Instant Customer Support** - Answer product questions immediately
- **Sales Assistance** - Help customers find the right products
- **Order Support** - Assist with order tracking and issues
- **24/7 Availability** - Support customers around the clock

---

## Installation Methods

### Method 1: Theme Code Editor (Recommended)

**Step 1: Access Theme Editor**
1. Log in to your Shopify admin
2. Go to **Online Store** → **Themes**
3. Find your current theme
4. Click **Actions** → **Edit Code**

**Step 2: Edit theme.liquid**
1. In the file list, find and click **Layout** → **theme.liquid**
2. Scroll to the bottom of the file
3. Find the closing `</body>` tag
4. Add your chat script just before it:

```html
<!-- Chat Assistant -->
<script async
  src="https://your-chat-script-url"
  project-id="project-id">
</script>
</body>
</html>
```

**Step 3: Save Changes**
1. Click **Save** in the top right
2. Visit your store to verify the chat appears

### Method 2: Custom Liquid Section

**Step 1: Create New Section**
1. In the theme editor, go to **Sections**
2. Click **Add a new section**
3. Name it `chat-assistant.liquid`

**Step 2: Add Section Code**
```liquid
<div class="chat-assistant-section">
  <script async
    src="https://your-chat-script-url"
    project-id="project-id">
  </script>
</div>

{% schema %}
{
  "name": "Chat Assistant",
  "settings": [
    {
      "type": "text",
      "id": "chat_id",
      "label": "Chat ID",
      "default": "your-default-chat-id"
    },
    {
      "type": "checkbox",
      "id": "show_on_mobile",
      "label": "Show on mobile",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "Chat Assistant"
    }
  ]
}
{% endschema %}
```

**Step 3: Add to Theme**
1. Go to **Online Store** → **Themes** → **Customize**
2. Add the "Chat Assistant" section
3. Configure the Chat ID in the section settings

---

## Product-Specific Integration

### Product Page Chat

Add chat specifically to product pages:

**Step 1: Edit product.liquid or product-form.liquid**
```liquid
<!-- Add after product form -->
<div class="product-chat">
  <h3>Questions about this product?</h3>
  <div class="chat-container">
    <script async
      src="https://your-chat-script-url"
      project-id="project-id">
    </script>
  </div>
</div>

<style>
.product-chat {
  margin-top: 20px;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 8px;
}

.product-chat h3 {
  margin-bottom: 10px;
  font-size: 18px;
}
</style>
```

### Collection Page Chat

Add to collection pages for category-specific support:

**Step 1: Edit collection.liquid**
```liquid
<!-- Add before or after product grid -->
<div class="collection-help">
  <div class="container">
    <h3>Need help finding the right {{ collection.title | downcase }}?</h3>
    <script async
      src="https://your-chat-script-url"
      project-id="project-id">
    </script>
  </div>
</div>
```

---

## Cart and Checkout Integration

### Cart Page Chat

**Edit cart.liquid:**
```liquid
<!-- Add to cart page -->
<div class="cart-assistance">
  <h3>Need help with your order?</h3>
  <p>Chat with us for shipping questions, product recommendations, or order assistance.</p>
  <script async
    src="https://your-chat-script-url"
    project-id="project-id">
  </script>
</div>

<style>
.cart-assistance {
  background: #e8f4fd;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  text-align: center;
}
</style>
```

### Checkout Chat (Shopify Plus)

For Shopify Plus stores, add to checkout.liquid:

```liquid
<!-- Add to checkout page -->
<div class="checkout-help">
  <script async
    src="https://your-chat-script-url"
    project-id="project-id">
  </script>
</div>
```

---

## Advanced Shopify Integration

### Customer Account Integration

**Edit customers/account.liquid:**
```liquid
<div class="account-support">
  <h3>Account Support</h3>
  <script async
    src="https://your-chat-script-url"
    project-id="project-id">
  </script>
</div>
```

### Order Status Integration

**Edit customers/order.liquid:**
```liquid
<div class="order-support">
  <h3>Questions about order #{{ order.name }}?</h3>
  <script async
    src="https://your-chat-script-url"
    project-id="project-id">
  </script>
</div>
```

### Blog Integration

**Edit article.liquid:**
```liquid
<!-- Add after article content -->
<div class="article-chat">
  <h3>Questions about this article?</h3>
  <script async
    src="https://your-chat-script-url"
    project-id="project-id">
  </script>
</div>
```

---

## Conditional Display

### Show/Hide Based on Conditions

```liquid
<!-- Only show on product pages -->
{% if template contains 'product' %}
  <script async
    src="https://your-chat-script-url"
    project-id="project-id">
  </script>
{% endif %}

<!-- Only show for logged-in customers -->
{% if customer %}
  <script async
    src="https://your-chat-script-url"
    project-id="project-id">
  </script>
{% endif %}

<!-- Only show during business hours -->
{% assign current_hour = 'now' | date: '%H' | plus: 0 %}
{% if current_hour >= 9 and current_hour <= 17 %}
  <script async
    src="https://your-chat-script-url"
    project-id="project-id">
  </script>
{% endif %}

<!-- Hide on specific pages -->
{% unless template == 'page.about' %}
  <script async
    src="https://your-chat-script-url"
    project-id="project-id">
  </script>
{% endunless %}
```

### Mobile/Desktop Specific

```liquid
<!-- Desktop only -->
<div class="hidden-mobile">
  <script async
    src="https://your-chat-script-url"
    project-id="project-id">
  </script>
</div>

<!-- Mobile only -->
<div class="hidden-desktop">
  <script async
    src="https://your-chat-script-url"
    project-id="project-id">
  </script>
</div>
```

---

## Styling for Shopify Themes

### Theme-Specific Adjustments

```css
/* Add to your theme's CSS file or in theme settings */

/* Adjust for common Shopify themes */

/* Dawn theme adjustments */
#ai-chat-widget {
  z-index: 1000;
}

/* Debut theme adjustments */
.template-index #ai-chat-widget {
  bottom: 20px;
}

/* Brooklyn theme adjustments */
.site-footer + #ai-chat-widget {
  bottom: 30px;
}

/* Mobile adjustments */
@media screen and (max-width: 749px) {
  #ai-chat-widget {
    bottom: 15px !important;
    right: 15px !important;
  }
}

/* Cart drawer compatibility */
.cart-drawer-open #ai-chat-widget {
  z-index: 999;
}

/* Search overlay compatibility */
.search-overlay-open #ai-chat-widget {
  display: none;
}
```

### Custom Chat Styling

```css
/* Custom chat button styling */
#ai-chat-widget-button {
  background: var(--color-accent) !important;
  border-radius: 50% !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

#ai-chat-widget-button:hover {
  transform: scale(1.05) !important;
  transition: transform 0.2s ease !important;
}

/* Custom chat window styling */
.chat-window {
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12) !important;
}

/* Match Shopify theme colors */
.chat-header {
  background: var(--color-accent) !important;
}

.chat-message-bot {
  background: var(--color-background) !important;
  color: var(--color-foreground) !important;
}
```

---

## Performance Optimization

### Lazy Loading for Shopify

```liquid
<!-- Add to theme.liquid before </body> -->
<script>
// Lazy load chat after page interaction
let chatLoaded = false;

function loadChat() {
  if (!chatLoaded) {
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-chat-script-url';
    script.setAttribute('project-id', 'your-chat-id');
    document.body.appendChild(script);
    chatLoaded = true;
  }
}

// Load chat on scroll or after 5 seconds
window.addEventListener('scroll', loadChat, { once: true });
setTimeout(loadChat, 5000);

// Load chat on any user interaction
['click', 'touchstart', 'keydown'].forEach(event => {
  document.addEventListener(event, loadChat, { once: true });
});
</script>
```

### Preload for Better Performance

```liquid
<!-- Add to <head> section -->
<link rel="preload" href="https://your-chat-script-url" as="script">
```

---

## Analytics Integration

### Shopify Analytics

```liquid
<!-- Track chat interactions with Shopify analytics -->
<script>
window.chatAnalytics = {
  trackEvent: function(event, data) {
    // Send to Shopify Analytics
    if (typeof analytics !== 'undefined') {
      analytics.track(event, data);
    }
    
    // Send to Google Analytics if available
    if (typeof gtag !== 'undefined') {
      gtag('event', event, data);
    }
  }
};
</script>
```

---

## Troubleshooting

### Common Shopify Issues

**Theme Updates Overwriting Code**
- Use sections instead of direct theme.liquid edits
- Create backup of customizations
- Document all changes made

**Checkout Script Restrictions**
- Shopify Plus required for checkout.liquid access
- Use cart page integration as alternative
- Consider post-purchase follow-up

**Mobile Theme Issues**
- Test on actual mobile devices
- Check theme's mobile CSS
- Verify touch interactions work

**App Conflicts**
- Disable other chat apps temporarily
- Check for JavaScript conflicts
- Test with minimal apps installed

### Performance Issues

**Slow Loading**
- Use lazy loading techniques
- Optimize script placement
- Consider conditional loading

**High Bounce Rate**
- Don't load chat immediately
- Use scroll-triggered loading
- Optimize for mobile experience

---

## Shopify App Alternative

### Custom App Development

For advanced integration, consider developing a Shopify app:

```javascript
// Basic Shopify app structure
const express = require('express');
const { Shopify } = require('@shopify/shopify-api');

const app = express();

app.get('/install-chat', async (req, res) => {
  // Install chat script via Shopify API
  const script = await Shopify.rest.ScriptTag.save({
    session: res.locals.shopify.session,
    src: 'https://your-chat-script-url',
    event: 'onload'
  });
  
  res.json({ success: true, script_id: script.id });
});
```

---