import store from "store2";

// 账单计划相关接口
export enum IntervalType {
  Monthly = "Monthly",
  Yearly = "Yearly",
}

export interface ProductResponse {
  id: string;
  name: string;
  type: string;
  interval: IntervalType;
  description: string;
  responses: number;
  bots: number;
  teamMembers: number;
  branding: boolean;
  documents: number;
  popular: number;
  price: number;
  paymentLink: string;
  createTime: string;
  additionalInfo: string;
}

export interface PlanLimits {
  aiChatbots: {
    used: number;
    total: number;
  };
  monthlyResponses: {
    used: number;
    total: number;
  };
  additionalCredits: number;
}

export interface BotLimits {
  trainingResources: {
    used: number;
    total: number;
  };
  seatsPerChatbot: {
    used: number;
    total: number;
  };
}

// 当前产品信息接口
export interface CurrentProductResponse {
  productId: string;
  productName: string;
  expireTime: string;
}

export interface CurrentPlan extends ProductResponse {
  renewalDate?: string;
  limits?: PlanLimits;
  botLimits?: BotLimits;
}

// AvailablePlan 现在就是 ProductResponse 的别名
export type AvailablePlan = ProductResponse;

// 升级计划
export interface UpgradePlanRequest {
  planId: string;
  billingCycle: "monthly" | "annually";
}

// 获取当前用户的计划信息
export const getCurrentPlan = async (): Promise<CurrentPlan> => {
  return new Promise((resolve) => {
    // 模拟异步延迟
    setTimeout(() => {
      const mockCurrentPlan: CurrentPlan = {
        id: "plan-free-001",
        name: "Free Plan",
        type: "Free",
        interval: IntervalType.Monthly,
        description: "Perfect for getting started with basic features",
        responses: 1000,
        bots: 1,
        teamMembers: 3,
        branding: false,
        documents: 2000,
        popular: 0,
        price: 0,
        paymentLink: "",
        createTime: "2024-01-01T00:00:00Z",
        additionalInfo: "",
        renewalDate: new Date(
          Date.now() + 30 * 24 * 60 * 60 * 1000
        ).toLocaleDateString(), // 30天后
        limits: {
          aiChatbots: {
            used: 1,
            total: 1,
          },
          monthlyResponses: {
            used: 245,
            total: 1000,
          },
          additionalCredits: 0,
        },
        botLimits: {
          trainingResources: {
            used: 26,
            total: 2000,
          },
          seatsPerChatbot: {
            used: 1,
            total: 3,
          },
        },
      };
      resolve(mockCurrentPlan);
    }, 500); // 模拟500ms的网络延迟
  });

  // const response = await fetch("/v1/products/current", {
  //   method: "GET",
  //   headers: {
  //     Authorization: `Bearer ${store.get("token")}`,
  //     "Content-Type": "application/json",
  //   },
  // });

  // if (!response.ok) {
  //   const errorData = await response.json().catch(() => ({}));
  //   throw new Error(
  //     errorData.message || `HTTP error! status: ${response.status}`
  //   );
  // }

  // return response.json();
};

// 获取所有可用的计划
export const getAvailablePlans = async (): Promise<AvailablePlan[]> => {
  const response = await fetch("/v1/products", {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};

export const upgradePlan = async (data: UpgradePlanRequest): Promise<void> => {
  const response = await fetch("/v1/products/upgrade", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }
};

// 获取当前产品信息
export const getCurrentProduct = async (): Promise<CurrentProductResponse> => {
  const response = await fetch("/v1/current-product", {
    method: "GET",
    headers: {
      Authorization: `Bearer ${store.get("token")}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};
