import store from "store2";
import {
  FormConfig,
  FormSubmission,
  FormResponse,
  TemplateResponse,
  AddFormRequest,
  UpdateFormRequest,
  Page,
} from "../../pages/FormBuilder/types/form-types";

// 表单API服务
export class FormApiService {
  private static instance: FormApiService;

  private constructor() {
    // FormApiService 单例模式
  }

  public static getInstance(): FormApiService {
    if (!FormApiService.instance) {
      FormApiService.instance = new FormApiService();
    }
    return FormApiService.instance;
  }

  // 获取表单模板
  async getFormTemplates(pageNumber?: number, pageSize?: number): Promise<TemplateResponse[] | Page<TemplateResponse>> {
    try {
      let url = `/v1/formTemplates`;
      const params = new URLSearchParams();

      if (pageNumber !== undefined) {
        params.append('pageNumber', pageNumber.toString());
      }

      if (pageSize !== undefined) {
        params.append('pageSize', pageSize.toString());
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${store.get("token")}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      return await response.json();
    } catch (error) {
      throw new Error(`${error}`);
    }
  }

  // 保存表单配置
  async saveForm(
    projectId: string,
    formConfig: FormConfig
  ): Promise<FormConfig> {
    try {
      // 检查是否为更新操作（表单已存在）
      const isUpdate = await this.formExists(formConfig.formId);

      if (isUpdate) {
        return await this.updateForm(formConfig.formId, formConfig);
      } else {
        return await this.createForm(projectId, formConfig);
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  }

  // 创建新表单
  private async createForm(
    projectId: string,
    formConfig: FormConfig
  ): Promise<FormConfig> {
    const requestData: AddFormRequest = {
      projectId,
      name: formConfig.title,
      description: formConfig.description,
      url: formConfig.url,
      logo: formConfig.logo,
      content: JSON.stringify({
        settings: formConfig.settings,
        components: formConfig.components,
      }),
      active: formConfig.active || false,
    };

    const response = await fetch(`/v1/forms`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${store.get("token")}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `HTTP error! status: ${response.status}`
      );
    }

    const formResponse: FormResponse = await response.json();
    return this.convertFormResponseToConfig(formResponse);
  }

  // 更新表单
  private async updateForm(
    formId: string,
    formConfig: FormConfig
  ): Promise<FormConfig> {
    const requestData: UpdateFormRequest = {
      name: formConfig.title,
      description: formConfig.description,
      logo: formConfig.logo,
      content: JSON.stringify({
        settings: formConfig.settings,
        components: formConfig.components,
      }),
    };

    const response = await fetch(`/v1/forms/${formId}`, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${store.get("token")}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message || `HTTP error! status: ${response.status}`
      );
    }

    const formResponse: FormResponse = await response.json();
    return this.convertFormResponseToConfig(formResponse);
  }

  // 检查表单是否存在
  private async formExists(formId: string): Promise<boolean> {
    try {
      await this.loadForm(formId);
      return true;
    } catch {
      return false;
    }
  }

  // 转换后端响应为前端配置格式
  private convertFormResponseToConfig(formResponse: FormResponse): FormConfig {
    let parsedContent: any;
    try {
      parsedContent = JSON.parse(formResponse.content);
    } catch {
      // 如果解析失败，使用默认值
      parsedContent = {
        settings: {
          submitText: "Submit",
          successMessage: "Form submitted successfully!",
          errorMessage: "Submitted failed, please try again.",
          allowMultipleSubmissions: true,
          requireLogin: false,
          collectEmail: false,
        },
        url: "",
        components: [],
      };
    }

    return {
      formId: formResponse.id,
      title: formResponse.name,
      description: formResponse.description,
      logo: formResponse.logo,
      settings: parsedContent.settings,
      url: parsedContent.url,
      components: parsedContent.components || [],
      createdAt: formResponse.createTime,
      updatedAt: formResponse.updateTime,
      active: formResponse.active,
    };
  }

  // 加载表单配置
  async loadForm(formId: string): Promise<FormConfig> {
    try {
      const response = await fetch(`/v1/forms`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${store.get("token")}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const forms: FormResponse[] = await response.json();
      const form = forms.find((f) => f.id === formId);

      if (!form) {
        throw new Error("Form not found");
      }

      return this.convertFormResponseToConfig(form);
    } catch (error) {
      throw new Error(`${error}`);
    }
  }

  // 删除表单
  async deleteForm(formId: string): Promise<void> {
    try {
      const response = await fetch(`/v1/forms/${formId}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${store.get("token")}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  }

  // 获取项目的所有表单
  async getProjectForms(projectId: string, pageNumber?: number, pageSize?: number): Promise<FormConfig[] | Page<FormConfig>> {
    try {
      let url = `/v1/forms`;
      const params = new URLSearchParams();

      if (pageNumber !== undefined) {
        params.append('pageNumber', pageNumber.toString());
      }

      if (pageSize !== undefined) {
        params.append('pageSize', pageSize.toString());
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${store.get("token")}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const result = await response.json();

      // 检查是否为分页响应
      if (result.items && Array.isArray(result.items)) {
        // 分页响应
        const forms: FormResponse[] = result.items;
        const projectForms = forms.filter((form) => form.projectId === projectId);
        const convertedForms = projectForms.map((form) => this.convertFormResponseToConfig(form));

        return {
          items: convertedForms,
          total: projectForms.length, // 注意：这里应该是过滤后的总数
          pageNumber: result.pageNumber,
          pageSize: result.pageSize,
          totalPages: result.totalPages
        };
      } else {
        // 非分页响应
        const forms: FormResponse[] = result;
        const projectForms = forms.filter((form) => form.projectId === projectId);
        return projectForms.map((form) => this.convertFormResponseToConfig(form));
      }
    } catch (error) {
      throw new Error(`${error}`);
    }
  }

  // 提交表单数据
  async submitForm(
    formId: string,
    data: Record<string, any>
  ): Promise<FormSubmission> {
    try {
      const response = await fetch(`/v1/submissions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          formId,
          content: JSON.stringify(data),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const result = await response.json();

      const submission: FormSubmission = {
        id: result.id,
        formId: result.formId,
        content: result.content,
        createTime: result.createTime,
        userAgent: navigator.userAgent,
        ipAddress: "127.0.0.1", // 在实际应用中由后端获取
      };

      return submission;
    } catch (error) {
      throw new Error(`${error}`);
    }
  }

  // 激活表单
  async setActive(formId: string): Promise<FormResponse> {
    try {
      const response = await fetch(`/v1/forms/${formId}/active`, {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${store.get("token")}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      return await response.json();
    } catch (error) {
      throw new Error(`${error}`);
    }
  }

  // 获取表单提交数据
  async getFormSubmissions(formId: string, pageNumber?: number, pageSize?: number): Promise<Page<FormSubmission>> {
    try {
      let url = `/v1/submissions?formId=${formId}`;
      
      if (pageNumber !== undefined) {
        url += `&pageNumber=${pageNumber}`;
      }
      
      if (pageSize !== undefined) {
        url += `&pageSize=${pageSize}`;
      }

      const response = await fetch(url, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${store.get("token")}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      return await response.json();
    } catch (error) {
      throw new Error(`${error}`);
    }
  }


  // 复制表单
  async duplicateForm(formId: string, newTitle?: string): Promise<FormConfig> {
    try {
      const originalForm = await this.loadForm(formId);

      // 从所有表单中找到原表单以获取项目ID
      const response = await fetch(`/v1/forms`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${store.get("token")}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to get form information");
      }

      const forms: FormResponse[] = await response.json();
      const originalFormResponse = forms.find((f) => f.id === formId);

      if (!originalFormResponse) {
        throw new Error("Original form not found");
      }

      const duplicatedForm: FormConfig = {
        ...originalForm,
        formId: `form_${Date.now()}_${Math.random()
          .toString(36)
          .substring(2, 11)}`,
        title: newTitle || `${originalForm.title} (副本)`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return await this.saveForm(
        originalFormResponse.projectId,
        duplicatedForm
      );
    } catch (error) {
      throw new Error(`${error}`);
    }
  }

  // // 导出表单数据
  // async exportFormData(
  //   formId: string,
  //   format: "json" | "csv" = "json"
  // ): Promise<string> {
  //   try {
  //     const submissions = await this.getFormSubmissions(formId);

  //     if (format === "csv") {
  //       return this.convertToCSV(submissions);
  //     }

  //     return JSON.stringify(submissions, null, 2);
  //   } catch (error) {
  //     throw new Error(`${error}`);
  //   }
  // }

  // // 转换为CSV格式
  // private convertToCSV(submissions: FormSubmission[]): string {
  //   if (submissions.length === 0) {
  //     return "";
  //   }

  //   // 获取所有字段名
  //   const allFields = new Set<string>();
  //   submissions.forEach((submission) => {
  //     Object.keys(submission.content).forEach((field) => allFields.add(field));
  //   });

  //   const fields = Array.from(allFields);
  //   const headers = ["提交ID", "提交时间", ...fields];

  //   // 构建CSV内容
  //   const csvContent = [
  //     headers.join(","),
  //     ...submissions.map((submission) =>
  //       [
  //         submission.id,
  //         submission.createTime,
  //         ...fields.map((field) => {
  //           const value = submission.content[field];
  //           // 处理包含逗号的值
  //           return typeof value === "string" && value.includes(",")
  //             ? `"${value.replace(/"/g, '""')}"`
  //             : value || "";
  //         }),
  //       ].join(",")
  //     ),
  //   ].join("\n");

  //   return csvContent;
  // }
}

// 导出单例实例
export const formApi = FormApiService.getInstance();
