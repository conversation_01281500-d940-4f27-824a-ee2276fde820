import React from "react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Phone } from "lucide-react";

const WhatsApp = () => {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <Phone className="h-8 w-8 text-green-600" />
          <h1 className="text-3xl font-bold">WhatsApp Integration</h1>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>WhatsApp Business API</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              Connect your WhatsApp Business account to provide customer support through WhatsApp.
              This feature is coming soon.
            </p>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default WhatsApp;
