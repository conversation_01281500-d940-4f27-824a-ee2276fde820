# Wix Installation

Add the chat assistant to your Wix website using custom code injection.

## Overview

Wix installation methods:
- **Custom Code (Recommended)** - Site-wide installation
- **HTML Element** - Page-specific installation  
- **Wix Code (Velo)** - Advanced integration with dynamic functionality
- **Embed Element** - For embedded chat components

---

## Method 1: Custom Code (Site-wide)

### Step 1: Access Custom Code Settings

1. **Open Wix Editor**
   - Log in to your Wix account
   - Click **Edit Site** for your website

2. **Navigate to Custom Code**
   - Click **Settings** in the top menu
   - Select **Custom Code** from the dropdown

3. **Add New Code**
   - Click **+ Add Custom Code**
   - Choose **Body - end** for the location
   - Name it "Chat Assistant"

### Step 2: Add Your Chat Script

```html
<script async
  src="https://your-chat-script-url"
  project-id="project-id">
</script>
```

### Step 3: Configure Settings

- **Code Type**: Choose "Essential" or "Functional"
- **Load on**: Select "All pages" or specific pages
- **Apply to**: Choose "All pages" for site-wide installation

### Step 4: Save and Publish

1. Click **Apply**
2. **Publish** your site
3. Visit your website to verify the chat appears

---

## Method 2: HTML Element (Page-specific)

### Step 1: Add HTML Element

1. **In Wix Editor**
   - Click **Add** (+) on the left panel
   - Go to **Embed** → **HTML iframe**
   - Drag the HTML element to your page

### Step 2: Configure HTML Element

1. **Click on the HTML element**
2. **Click "Enter Code"**
3. **Add your chat script:**

```html
<div id="wix-chat-container">
  <script async
    src="https://your-chat-script-url"
    project-id="project-id">
  </script>
</div>

<style>
#wix-chat-container {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
```

### Step 3: Adjust Element Settings

- **Size**: Set appropriate width and height
- **Position**: Place where you want the chat
- **Responsive**: Configure for mobile devices

---

## Method 3: Wix Code (Velo) Integration

### Step 1: Enable Wix Code

1. **In Wix Editor**
   - Click **Dev Mode** in the top menu
   - Enable **Wix Code** (now called Velo)

### Step 2: Add to Page Code

**In your page's code panel:**

```javascript
import { session } from 'wix-storage-frontend';

$w.onReady(function () {
    // Load chat assistant
    loadChatAssistant();
    
    // Track user interactions
    trackUserBehavior();
});

function loadChatAssistant() {
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-chat-script-url';
    script.setAttribute('project-id', 'your-unique-chat-id');
    
    // Add user context if available
    const userEmail = session.getItem('userEmail');
    if (userEmail) {
            user: { email: userEmail }
        }));
    }
    
    document.body.appendChild(script);
}

function trackUserBehavior() {
    // Track page views for chat context
    console.log('Page loaded:', $w('#page').id);
    
    // You can send this data to your chat system
    // for better context-aware responses
}
```

### Step 3: Add User Context

```javascript
// Get user information from Wix
import { currentMember } from 'wix-members-frontend';

currentMember.getMember()
    .then((member) => {
        if (member) {
            // Pass member info to chat
            const chatOptions = {
                user: {
                    id: member._id,
                    email: member.loginEmail,
                    name: `${member.contactDetails.firstName} ${member.contactDetails.lastName}`
                }
            };
            
            // Update chat with user context
            updateChatContext(chatOptions);
        }
    });
```

---

## Method 4: Embedded Chat Component

### Step 1: Create Embedded Chat

1. **Add HTML Element** for embedded chat
2. **Configure as container:**

```html
<div id="wix-embedded-chat" style="width: 100%; height: 400px; border: 1px solid #ddd; border-radius: 8px;">
  <script async
    src="https://your-chat-script-url"
    project-id="project-id"
    data-target="wix-embedded-chat"
    >
  </script>
</div>

<style>
#wix-embedded-chat {
    background: #f9f9f9;
    overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #wix-embedded-chat {
        height: 300px;
    }
}
</style>
```

### Step 2: Style Integration

```css
/* Match Wix theme colors */
.chat-container {
    font-family: var(--wix-font-family);
    color: var(--wix-color-text);
}

.chat-header {
    background: var(--wix-color-primary);
}

.chat-message {
    border-radius: 12px;
}
```

---

## Wix-Specific Features

### Contact Form Integration

**Connect chat with Wix forms:**

```javascript
// In Wix Code
import { contacts } from 'wix-crm-frontend';

$w('#contactForm').onWixFormSubmitted((event) => {
    const formData = event.formData;
    
    // Send form data to chat system
    if (window.chatAPI) {
        window.chatAPI.setContext({
            formSubmission: {
                name: formData.name,
                email: formData.email,
                message: formData.message,
                timestamp: new Date().toISOString()
            }
        });
    }
});
```

### Wix Stores Integration

**For e-commerce sites:**

```javascript
import { cart } from 'wix-stores-frontend';

// Track cart changes
cart.onChange((cartData) => {
    if (window.chatAPI) {
        window.chatAPI.setContext({
            cart: {
                items: cartData.lineItems.length,
                total: cartData.totals.total,
                currency: cartData.currency
            }
        });
    }
});

// Product page context
$w.onReady(() => {
    if ($w('#productPage').isVisible) {
        const productId = $w('#productPage').getProduct().productId;
        
        if (window.chatAPI) {
            window.chatAPI.setContext({
                product: { id: productId }
            });
        }
    }
});
```

### Wix Bookings Integration

```javascript
import { bookings } from 'wix-bookings-frontend';

// Track booking events
bookings.onBookingCreated((booking) => {
    if (window.chatAPI) {
        window.chatAPI.setContext({
            booking: {
                id: booking._id,
                service: booking.bookedEntity.title,
                date: booking.bookedEntity.schedule.start
            }
        });
    }
});
```

---

## Responsive Design for Wix

### Mobile Optimization

```css
/* Wix mobile breakpoints */
@media (max-width: 980px) {
    #ai-chat-widget {
        bottom: 15px !important;
        right: 15px !important;
        width: 60px !important;
        height: 60px !important;
    }
}

@media (max-width: 640px) {
    #ai-chat-widget {
        bottom: 10px !important;
        right: 10px !important;
        width: 50px !important;
        height: 50px !important;
    }
}

/* Wix editor compatibility */
.wix-iframe #ai-chat-widget {
    position: fixed !important;
    z-index: 9999 !important;
}
```

### Wix App Market Compatibility

```javascript
// Ensure compatibility with Wix apps
$w.onReady(() => {
    // Wait for Wix apps to load
    setTimeout(() => {
        loadChatAssistant();
    }, 2000);
});

function loadChatAssistant() {
    // Check if other chat widgets exist
    const existingChat = document.querySelector('.wix-chat-widget');
    if (existingChat) {
        console.log('Another chat widget detected');
        return;
    }
    
    // Load your chat
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-chat-script-url';
    script.setAttribute('project-id', 'your-unique-chat-id');
    document.body.appendChild(script);
}
```

---

## Advanced Wix Integration

### Multi-language Support

```javascript
// Detect Wix site language
import { multilingual } from 'wix-window-frontend';

multilingual.currentLanguage
    .then((language) => {
        const chatOptions = {
            language: language,
            locale: language
        };
        
        // Load language-specific chat
        loadLocalizedChat(chatOptions);
    });

function loadLocalizedChat(options) {
    const script = document.createElement('script');
    script.async = true;
    script.src = 'https://your-chat-script-url';
    script.setAttribute('project-id', 'your-unique-chat-id');
    document.body.appendChild(script);
}
```

### Wix SEO Integration

```javascript
// Add structured data for chat support
const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "availableLanguage": ["English"],
        "url": window.location.href
    }
};

// Add to page head
const script = document.createElement('script');
script.type = 'application/ld+json';
script.text = JSON.stringify(structuredData);
document.head.appendChild(script);
```

---

## Troubleshooting Wix Issues

### Common Problems

**Chat Not Loading**
- Check custom code is set to "Body - end"
- Verify code is applied to correct pages
- Clear Wix cache and republish

**Wix Editor Conflicts**
- Chat may not appear in editor preview
- Always test on published site
- Use preview mode for testing

**Mobile Issues**
- Test on actual mobile devices
- Check Wix mobile editor settings
- Verify responsive breakpoints

**App Conflicts**
- Disable other chat/popup apps
- Check for JavaScript conflicts
- Test with minimal apps installed

### Performance Optimization

**Lazy Loading**
```javascript
// Load chat after user interaction
let chatLoaded = false;

function loadChatOnInteraction() {
    if (!chatLoaded) {
        const script = document.createElement('script');
        script.async = true;
        script.src = 'https://your-chat-script-url';
        script.setAttribute('project-id', 'your-unique-chat-id');
        document.body.appendChild(script);
        chatLoaded = true;
    }
}

// Load on scroll or click
$w.onReady(() => {
    $w(document).onScroll(() => {
        loadChatOnInteraction();
    });
    
    $w(document).onClick(() => {
        loadChatOnInteraction();
    });
});
```

**Conditional Loading**
```javascript
// Only load on specific pages
$w.onReady(() => {
    const currentPage = $w('#page').id;
    const chatPages = ['home', 'contact', 'products'];
    
    if (chatPages.includes(currentPage)) {
        loadChatAssistant();
    }
});
```

---

## Testing Your Installation

### Verification Steps

1. **Preview Mode**: Test in Wix preview mode
2. **Published Site**: Always test on live site
3. **Mobile Testing**: Check on mobile devices
4. **Different Browsers**: Test cross-browser compatibility
5. **Page Speed**: Monitor loading performance

### Debug Mode

```javascript
// Add debug logging
window.chatDebug = true;

function debugLog(message, data) {
    if (window.chatDebug) {
        console.log('[Chat Debug]', message, data);
    }
}

// Use throughout your chat integration
debugLog('Chat loading started');
debugLog('User context:', userContext);
```

---
