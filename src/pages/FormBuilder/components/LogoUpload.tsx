import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { Upload, X, Image } from "lucide-react";

interface LogoUploadProps {
  value?: string; // base64 encoded image
  onChange: (logo: string | null) => void;
  disabled?: boolean;
}

export const LogoUpload = ({ value, onChange, disabled = false }: LogoUploadProps) => {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { t } = useTranslation();

  // 验证文件类型和大小
  const validateFile = (file: File): boolean => {
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
      toast({
        variant: "destructive",
        title: t("logoUploadError"),
        description: t("logoFormatError"),
      });
      return false;
    }

    // 检查文件大小 (2MB)
    const maxSize = 2 * 1024 * 1024;
    if (file.size > maxSize) {
      toast({
        variant: "destructive",
        title: t("logoUploadError"),
        description: t("logoSizeError"),
      });
      return false;
    }

    return true;
  };

  // 将文件转换为 base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result);
        } else {
          reject(new Error('Failed to read file'));
        }
      };
      reader.onerror = () => reject(reader.error);
      reader.readAsDataURL(file);
    });
  };

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    if (!validateFile(file)) {
      return;
    }

    try {
      const base64 = await fileToBase64(file);
      onChange(base64);
      toast({
        title: t("uploadSuccess"),
        description: t("logoUploadSuccess"),
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: t("logoUploadError"),
        description: error instanceof Error ? error.message : t("logoUploadError"),
      });
    }
  };

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
    // 清空 input 值，允许重复选择同一文件
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 处理拖拽
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
    
    if (disabled) return;

    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  // 点击上传
  const handleUploadClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 移除 logo
  const handleRemove = () => {
    onChange(null);
  };

  return (
    <div className="space-y-2">
      <Label className="text-sm font-medium">{t("formLogo")}</Label>
      
      <div className="flex items-center space-x-4">
        {/* Logo 预览区域 */}
        <div
          className={`
            relative w-16 h-16 border-2 border-dashed rounded-lg flex items-center justify-center
            ${isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-gray-400'}
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleUploadClick}
        >
          {value ? (
            <img
              src={value}
              alt="Form Logo"
              className="w-full h-full object-cover rounded-lg"
            />
          ) : (
            <Image className="w-6 h-6 text-gray-400" />
          )}
          
          {/* 移除按钮 */}
          {value && !disabled && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleRemove();
              }}
              className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
            >
              <X className="w-3 h-3" />
            </button>
          )}
        </div>

        {/* 上传按钮 */}
        <div className="flex flex-col space-y-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleUploadClick}
            disabled={disabled}
            className="flex items-center space-x-2"
          >
            <Upload className="w-4 h-4" />
            <span>{value ? t("changeLogo") : t("uploadLogo")}</span>
          </Button>
          
          <p className="text-xs text-gray-500">
            {t("logoUploadTip")}
          </p>
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png"
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />
    </div>
  );
};
