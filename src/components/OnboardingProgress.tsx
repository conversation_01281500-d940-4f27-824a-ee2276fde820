import { cn } from '@/lib/utils';

interface OnboardingProgressProps {
  currentStep: number;
  totalSteps?: number;
  className?: string;
}

export const OnboardingProgress = ({
                                     currentStep,
                                     totalSteps = 4,
                                     className
                                   }: OnboardingProgressProps) => {
  return (
      <div
          className={cn(
              "flex items-center justify-center py-8 bg-white/80 backdrop-blur-sm border-b",
              className
          )}
      >
        <div className="flex items-center space-x-2">
          {Array.from({ length: totalSteps }, (_, index) => {
            const stepNumber = index + 1;
            const isCompleted = stepNumber < currentStep;
            const isActive = stepNumber === currentStep;
            const isLast = stepNumber === totalSteps;

            return (
                <div key={stepNumber} className="flex items-center">
                  <div
                      className={cn(
                          "w-9 h-9 rounded-full flex items-center justify-center text-sm font-semibold transition-all duration-300",
                          isActive
                              ? "bg-gradient-to-r from-primary to-primary/80 text-white ring-4 ring-primary/30 shadow-lg scale-110"
                              : isCompleted
                                  ? "bg-gradient-to-r from-primary/90 to-primary/70 text-white"
                                  : "bg-gray-200 text-gray-500"
                      )}
                  >
                    {stepNumber}
                  </div>
                  {!isLast && (
                      <div
                          className={cn(
                              "h-0.5 w-20 transition-colors duration-300 rounded-full",
                              isCompleted
                                  ? "bg-[length:200%_100%] bg-gradient-to-r from-primary via-primary/70 to-primary animate-flow"
                                  : "bg-gray-300"
                          )}
                      />
                  )}
                </div>
            );
          })}
        </div>
      </div>
  );
};
