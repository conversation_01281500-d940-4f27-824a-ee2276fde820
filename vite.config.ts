import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import { docsPlugin } from "./src/utils/docs-loader";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    proxy: {
      '^/chat$': {
        target: 'http://localhost:10070', // 目标服务器地址
        changeOrigin: true,
      },
      '/v1': {
        target: 'http://localhost:10070', // 目标服务器地址
        changeOrigin: true,
      },
      '/static': {
        target: 'http://localhost:10070', // 目标服务器地址
        changeOrigin: true,
      }
    },
  },
  plugins: [
    react(),
    docsPlugin(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
