(() => {
    try {
      // 定义版本号，用于请求 URL 的缓存控制
      const version = "1.0.13";
      
      // 初始化全局状态对象
      window.test = { isDestroyed: false };
      
      // 获取当前脚本元素或通过 ID 获取
      const scriptElement = document.currentScript || document.getElementById("test-script");
      
      // 提取脚本的 dataset 属性
      const { dataset } = scriptElement;
      
      // 定义远程服务器基础 URL
      const baseUrl = "https://w.quidget.ai";
      
      // 发送请求获取文件列表
      fetchFileList(`${baseUrl}/file-list.json?v=${version}`)
        .then(fileList => {
          // 查找主应用文件（以 test. 开头，.js 结尾，不包含 loader）
          const mainFile = fileList.find(
            file => file.fileName.startsWith("test.") && 
                    file.fileName.endsWith(".js") && 
                    !file.fileName.includes("loader")
          );
          
          // 如果未找到主文件，抛出错误
          if (!mainFile) {
            throw new Error("Main application file not found");
          }
          
          // 动态创建 script 标签加载主应用文件
          const script = document.createElement("script");
          script.async = true;
          script.src = `${baseUrl}/${mainFile.fileName}`;
          
          // 将脚本追加到 head 或 documentElement
          (document.head || document.documentElement).appendChild(script);
          
          // 脚本加载完成后执行初始化
          script.onload = async () => {
            if (document.readyState === "complete") {
              // 文档已加载完成且未销毁，执行初始化
              if (!window.test.isDestroyed) {
                await window.test.init(dataset);
              }
              return;
            }
            
            // 文档未加载完成，监听 readyState 变化
            document.onreadystatechange = async () => {
              if (document.readyState === "complete" && !window.test.isDestroyed) {
                await window.test.init(dataset);
              }
            };
          };
        })
        .catch(error => {
          // 捕获加载错误并打印警告
          console.warn(`💬 fail on load test ${JSON.stringify(error)}`, error);
        });
    } catch (error) {
      // 捕获全局错误并打印
      console.error("Failed to load test loader:", error);
    }
  
    // 定义通用的 HTTP 请求函数
    function fetchFileList(url = "", { 
      method = "GET", 
      headers = {}, 
      params = {}, 
      parse = "json", 
      onUploadProgress 
    } = {}) {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open(method, url, true);
        xhr.responseType = parse;
        
        // 处理请求成功
        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve(xhr.response);
          } else {
            reject({ status: xhr.status, statusText: xhr.statusText });
          }
        };
        
        // 处理请求错误
        xhr.onerror = () => {
          reject({ status: xhr.status, statusText: xhr.statusText });
        };
        
        // 处理上传进度
        if (typeof onUploadProgress === "function") {
          xhr.upload.onprogress = onUploadProgress;
        }
        xhr.upload.onerror = () => {
          reject({ status: xhr.status, statusText: xhr.statusText });
        };
        
        // 设置请求头
        if (headers) {
          Object.keys(headers).forEach(key => xhr.setRequestHeader(key, headers[key]));
        }
        
        // 处理查询参数
        let queryString = params;
        if (params && typeof params === "object") {
          queryString = Object.keys(params)
            .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
            .join("&");
        }
        
        // 发送请求
        xhr.send(queryString);
      });
    }
  })();